'use client';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON>lus, FiFilter, FiDownload, FiDollarSign, FiCreditCard, FiArrowUp, FiArrowDown } from 'react-icons/fi';
import DashboardLayout from '../components/DashboardLayout';
import Card from '../components/ui/Card';
import Button from '../components/ui/Button';
import Table from '../components/ui/Table';
import Modal from '../components/ui/Modal';
import { Input, Select, Textarea, FormGroup, FormRow } from '../components/ui/Form';
import toast from 'react-hot-toast';

// Mock data for demonstration
const mockTransactions = [
  { id: 1, transactionNo: 'TRX-001', description: 'Sale - Order #ORD-001', amount: 125.50, transactionType: 'SALE', transactionDate: '2023-05-20', reference: 'ORD-001' },
  { id: 2, transactionNo: 'TRX-002', description: 'Purchase - Invoice #INV-001', amount: 500.00, transactionType: 'PURCHASE', transactionDate: '2023-05-19', reference: 'INV-001' },
  { id: 3, transactionNo: 'TRX-003', description: 'Expense - Rent', amount: 1200.00, transactionType: 'EXPENSE', transactionDate: '2023-05-18', reference: 'RENT-MAY' },
  { id: 4, transactionNo: 'TRX-004', description: 'Sale - Order #ORD-002', amount: 89.99, transactionType: 'SALE', transactionDate: '2023-05-17', reference: 'ORD-002' },
  { id: 5, transactionNo: 'TRX-005', description: 'Expense - Utilities', amount: 150.00, transactionType: 'EXPENSE', transactionDate: '2023-05-16', reference: 'UTIL-MAY' },
  { id: 6, transactionNo: 'TRX-006', description: 'Sale - Order #ORD-003', amount: 210.75, transactionType: 'SALE', transactionDate: '2023-05-15', reference: 'ORD-003' },
  { id: 7, transactionNo: 'TRX-007', description: 'Purchase - Invoice #INV-002', amount: 750.00, transactionType: 'PURCHASE', transactionDate: '2023-05-14', reference: 'INV-002' },
  { id: 8, transactionNo: 'TRX-008', description: 'Income - Refund', amount: 50.00, transactionType: 'INCOME', transactionDate: '2023-05-13', reference: 'REF-001' },
];

const mockAccounts = [
  { id: 1, name: 'Cash', accountType: 'ASSET', balance: 5000.00 },
  { id: 2, name: 'Bank Account', accountType: 'ASSET', balance: 12500.00 },
  { id: 3, name: 'Accounts Receivable', accountType: 'ASSET', balance: 3200.00 },
  { id: 4, name: 'Inventory', accountType: 'ASSET', balance: 15000.00 },
  { id: 5, name: 'Accounts Payable', accountType: 'LIABILITY', balance: 2800.00 },
  { id: 6, name: 'Sales Revenue', accountType: 'REVENUE', balance: 25000.00 },
  { id: 7, name: 'Cost of Goods Sold', accountType: 'EXPENSE', balance: 12000.00 },
  { id: 8, name: 'Rent Expense', accountType: 'EXPENSE', balance: 3600.00 },
  { id: 9, name: 'Utilities Expense', accountType: 'EXPENSE', balance: 450.00 },
];

export default function AccountingPage() {
  const [activeTab, setActiveTab] = useState('transactions');
  const [transactions, setTransactions] = useState([]);
  const [accounts, setAccounts] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isTransactionModalOpen, setIsTransactionModalOpen] = useState(false);
  const [isAccountModalOpen, setIsAccountModalOpen] = useState(false);
  const [currentTransaction, setCurrentTransaction] = useState(null);
  const [currentAccount, setCurrentAccount] = useState(null);
  const [filters, setFilters] = useState({
    type: '',
    dateRange: '',
  });
  const [transactionForm, setTransactionForm] = useState({
    description: '',
    amount: '',
    transactionType: 'SALE',
    transactionDate: new Date().toISOString().split('T')[0],
    reference: '',
    notes: '',
    accountId: '',
  });
  const [accountForm, setAccountForm] = useState({
    name: '',
    accountType: 'ASSET',
    description: '',
    balance: '0.00',
  });

  // Load data
  useEffect(() => {
    const timer = setTimeout(() => {
      setTransactions(mockTransactions);
      setAccounts(mockAccounts);
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  // Handle transaction form input changes
  const handleTransactionInputChange = (e) => {
    const { name, value } = e.target;
    setTransactionForm({
      ...transactionForm,
      [name]: value,
    });
  };

  // Handle account form input changes
  const handleAccountInputChange = (e) => {
    const { name, value } = e.target;
    setAccountForm({
      ...accountForm,
      [name]: value,
    });
  };

  // Open modal to add new transaction
  const handleAddTransaction = () => {
    setCurrentTransaction(null);
    setTransactionForm({
      description: '',
      amount: '',
      transactionType: 'SALE',
      transactionDate: new Date().toISOString().split('T')[0],
      reference: '',
      notes: '',
      accountId: '',
    });
    setIsTransactionModalOpen(true);
  };

  // Open modal to add new account
  const handleAddAccount = () => {
    setCurrentAccount(null);
    setAccountForm({
      name: '',
      accountType: 'ASSET',
      description: '',
      balance: '0.00',
    });
    setIsAccountModalOpen(true);
  };

  // Save transaction
  const handleSaveTransaction = () => {
    // Validate form
    if (!transactionForm.description || !transactionForm.amount || !transactionForm.transactionDate) {
      toast.error('Please fill in all required fields');
      return;
    }

    // Here you would normally save to the database
    if (currentTransaction) {
      // Update existing transaction
      setTransactions(transactions.map(t => 
        t.id === currentTransaction.id 
          ? { 
              ...t, 
              description: transactionForm.description,
              amount: parseFloat(transactionForm.amount),
              transactionType: transactionForm.transactionType,
              transactionDate: transactionForm.transactionDate,
              reference: transactionForm.reference,
              notes: transactionForm.notes,
            } 
          : t
      ));
      toast.success('Transaction updated successfully');
    } else {
      // Add new transaction
      const newTransaction = {
        id: Math.max(...transactions.map(t => t.id)) + 1,
        transactionNo: `TRX-${String(Math.max(...transactions.map(t => t.id)) + 1).padStart(3, '0')}`,
        description: transactionForm.description,
        amount: parseFloat(transactionForm.amount),
        transactionType: transactionForm.transactionType,
        transactionDate: transactionForm.transactionDate,
        reference: transactionForm.reference,
        notes: transactionForm.notes,
      };
      setTransactions([...transactions, newTransaction]);
      toast.success('Transaction added successfully');
    }

    setIsTransactionModalOpen(false);
  };

  // Save account
  const handleSaveAccount = () => {
    // Validate form
    if (!accountForm.name || !accountForm.accountType) {
      toast.error('Please fill in all required fields');
      return;
    }

    // Here you would normally save to the database
    if (currentAccount) {
      // Update existing account
      setAccounts(accounts.map(a => 
        a.id === currentAccount.id 
          ? { 
              ...a, 
              name: accountForm.name,
              accountType: accountForm.accountType,
              description: accountForm.description,
              balance: parseFloat(accountForm.balance),
            } 
          : a
      ));
      toast.success('Account updated successfully');
    } else {
      // Add new account
      const newAccount = {
        id: Math.max(...accounts.map(a => a.id)) + 1,
        name: accountForm.name,
        accountType: accountForm.accountType,
        description: accountForm.description,
        balance: parseFloat(accountForm.balance),
      };
      setAccounts([...accounts, newAccount]);
      toast.success('Account added successfully');
    }

    setIsAccountModalOpen(false);
  };

  // Apply filters to transactions
  const filteredTransactions = transactions.filter(transaction => {
    if (filters.type && transaction.transactionType !== filters.type) return false;
    // Add date range filtering if needed
    return true;
  });

  // Transaction table columns
  const transactionColumns = [
    { key: 'transactionNo', label: 'Transaction #' },
    { key: 'transactionDate', label: 'Date' },
    { key: 'description', label: 'Description' },
    { 
      key: 'transactionType', 
      label: 'Type',
      render: (row) => {
        const typeStyles = {
          SALE: 'bg-green-100 text-green-800',
          PURCHASE: 'bg-blue-100 text-blue-800',
          EXPENSE: 'bg-red-100 text-red-800',
          INCOME: 'bg-purple-100 text-purple-800',
          TRANSFER: 'bg-yellow-100 text-yellow-800',
          ADJUSTMENT: 'bg-gray-100 text-gray-800',
        };
        return (
          <span className={`px-2 py-1 text-xs rounded-full ${typeStyles[row.transactionType] || 'bg-gray-100 text-gray-800'}`}>
            {row.transactionType}
          </span>
        );
      },
    },
    { 
      key: 'amount', 
      label: 'Amount',
      render: (row) => {
        const isExpense = ['PURCHASE', 'EXPENSE'].includes(row.transactionType);
        return (
          <div className={`flex items-center ${isExpense ? 'text-red-500' : 'text-green-500'}`}>
            {isExpense ? <FiArrowDown className="mr-1" /> : <FiArrowUp className="mr-1" />}
            ${row.amount.toFixed(2)}
          </div>
        );
      },
    },
    { key: 'reference', label: 'Reference' },
  ];

  // Account table columns
  const accountColumns = [
    { key: 'name', label: 'Account Name' },
    { 
      key: 'accountType', 
      label: 'Type',
      render: (row) => {
        const typeStyles = {
          ASSET: 'bg-blue-100 text-blue-800',
          LIABILITY: 'bg-red-100 text-red-800',
          EQUITY: 'bg-purple-100 text-purple-800',
          REVENUE: 'bg-green-100 text-green-800',
          EXPENSE: 'bg-yellow-100 text-yellow-800',
        };
        return (
          <span className={`px-2 py-1 text-xs rounded-full ${typeStyles[row.accountType] || 'bg-gray-100 text-gray-800'}`}>
            {row.accountType}
          </span>
        );
      },
    },
    { 
      key: 'balance', 
      label: 'Balance',
      render: (row) => `$${row.balance.toFixed(2)}`,
    },
  ];

  return (
    <DashboardLayout title="Accounting">
      <div className="mb-6">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            <button
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'transactions'
                  ? 'border-primary text-primary'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
              onClick={() => setActiveTab('transactions')}
            >
              Transactions
            </button>
            <button
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'accounts'
                  ? 'border-primary text-primary'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
              onClick={() => setActiveTab('accounts')}
            >
              Chart of Accounts
            </button>
          </nav>
        </div>
      </div>

      {activeTab === 'transactions' ? (
        <Card>
          <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6 gap-4">
            <div className="flex flex-col sm:flex-row gap-4">
              <Select
                placeholder="Filter by Type"
                value={filters.type}
                onChange={(e) => setFilters({ ...filters, type: e.target.value })}
                options={[
                  { value: '', label: 'All Types' },
                  { value: 'SALE', label: 'Sales' },
                  { value: 'PURCHASE', label: 'Purchases' },
                  { value: 'EXPENSE', label: 'Expenses' },
                  { value: 'INCOME', label: 'Income' },
                  { value: 'TRANSFER', label: 'Transfers' },
                  { value: 'ADJUSTMENT', label: 'Adjustments' },
                ]}
                className="w-full sm:w-40"
              />
              <Select
                placeholder="Date Range"
                value={filters.dateRange}
                onChange={(e) => setFilters({ ...filters, dateRange: e.target.value })}
                options={[
                  { value: '', label: 'All Time' },
                  { value: 'today', label: 'Today' },
                  { value: 'week', label: 'This Week' },
                  { value: 'month', label: 'This Month' },
                  { value: 'year', label: 'This Year' },
                ]}
                className="w-full sm:w-40"
              />
            </div>
            
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => setFilters({ type: '', dateRange: '' })}
              >
                <FiFilter className="mr-1" /> Clear Filters
              </Button>
              <Button
                variant="outline"
              >
                <FiDownload className="mr-1" /> Export
              </Button>
              <Button
                onClick={handleAddTransaction}
              >
                <FiPlus className="mr-1" /> Add Transaction
              </Button>
            </div>
          </div>
          
          {isLoading ? (
            <div className="flex items-center justify-center h-64">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
            </div>
          ) : (
            <Table
              columns={transactionColumns}
              data={filteredTransactions}
              pagination={true}
              itemsPerPage={10}
            />
          )}
        </Card>
      ) : (
        <Card>
          <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6 gap-4">
            <h2 className="text-lg font-medium">Chart of Accounts</h2>
            
            <div className="flex gap-2">
              <Button
                variant="outline"
              >
                <FiDownload className="mr-1" /> Export
              </Button>
              <Button
                onClick={handleAddAccount}
              >
                <FiPlus className="mr-1" /> Add Account
              </Button>
            </div>
          </div>
          
          {isLoading ? (
            <div className="flex items-center justify-center h-64">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
            </div>
          ) : (
            <Table
              columns={accountColumns}
              data={accounts}
              pagination={true}
              itemsPerPage={10}
            />
          )}
        </Card>
      )}
      
      {/* Transaction Modal */}
      <Modal
        isOpen={isTransactionModalOpen}
        onClose={() => setIsTransactionModalOpen(false)}
        title={currentTransaction ? 'Edit Transaction' : 'Add New Transaction'}
        size="lg"
        footer={
          <>
            <Button
              variant="outline"
              onClick={() => setIsTransactionModalOpen(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSaveTransaction}
            >
              {currentTransaction ? 'Update Transaction' : 'Add Transaction'}
            </Button>
          </>
        }
      >
        <FormGroup>
          <FormRow>
            <Select
              label="Transaction Type"
              name="transactionType"
              value={transactionForm.transactionType}
              onChange={handleTransactionInputChange}
              options={[
                { value: 'SALE', label: 'Sale' },
                { value: 'PURCHASE', label: 'Purchase' },
                { value: 'EXPENSE', label: 'Expense' },
                { value: 'INCOME', label: 'Income' },
                { value: 'TRANSFER', label: 'Transfer' },
                { value: 'ADJUSTMENT', label: 'Adjustment' },
              ]}
              required
            />
            <Input
              label="Date"
              name="transactionDate"
              type="date"
              value={transactionForm.transactionDate}
              onChange={handleTransactionInputChange}
              required
            />
          </FormRow>
          
          <Input
            label="Description"
            name="description"
            value={transactionForm.description}
            onChange={handleTransactionInputChange}
            required
          />
          
          <FormRow>
            <Input
              label="Amount"
              name="amount"
              type="number"
              min="0"
              step="0.01"
              value={transactionForm.amount}
              onChange={handleTransactionInputChange}
              required
            />
            <Input
              label="Reference"
              name="reference"
              value={transactionForm.reference}
              onChange={handleTransactionInputChange}
              placeholder="Invoice #, Order #, etc."
            />
          </FormRow>
          
          <Select
            label="Account"
            name="accountId"
            value={transactionForm.accountId}
            onChange={handleTransactionInputChange}
            options={accounts.map(a => ({ value: a.id, label: a.name }))}
            required
          />
          
          <Textarea
            label="Notes"
            name="notes"
            value={transactionForm.notes}
            onChange={handleTransactionInputChange}
          />
        </FormGroup>
      </Modal>
      
      {/* Account Modal */}
      <Modal
        isOpen={isAccountModalOpen}
        onClose={() => setIsAccountModalOpen(false)}
        title={currentAccount ? 'Edit Account' : 'Add New Account'}
        size="md"
        footer={
          <>
            <Button
              variant="outline"
              onClick={() => setIsAccountModalOpen(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSaveAccount}
            >
              {currentAccount ? 'Update Account' : 'Add Account'}
            </Button>
          </>
        }
      >
        <FormGroup>
          <Input
            label="Account Name"
            name="name"
            value={accountForm.name}
            onChange={handleAccountInputChange}
            required
          />
          
          <Select
            label="Account Type"
            name="accountType"
            value={accountForm.accountType}
            onChange={handleAccountInputChange}
            options={[
              { value: 'ASSET', label: 'Asset' },
              { value: 'LIABILITY', label: 'Liability' },
              { value: 'EQUITY', label: 'Equity' },
              { value: 'REVENUE', label: 'Revenue' },
              { value: 'EXPENSE', label: 'Expense' },
            ]}
            required
          />
          
          <Textarea
            label="Description"
            name="description"
            value={accountForm.description}
            onChange={handleAccountInputChange}
          />
          
          <Input
            label="Opening Balance"
            name="balance"
            type="number"
            min="0"
            step="0.01"
            value={accountForm.balance}
            onChange={handleAccountInputChange}
          />
        </FormGroup>
      </Modal>
    </DashboardLayout>
  );
}
