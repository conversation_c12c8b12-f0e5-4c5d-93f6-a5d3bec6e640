import { NextResponse } from 'next/server';
import { prisma } from '../../lib/prisma';

export async function GET() {
  try {
    // Test the database connection
    const databaseInfo = await prisma.$queryRaw`SELECT VERSION() as version`;
    
    return NextResponse.json({ 
      status: 'success', 
      message: 'Database connection successful',
      databaseInfo
    });
  } catch (error) {
    console.error('Database connection error:', error);
    return NextResponse.json(
      { 
        status: 'error', 
        message: 'Database connection failed', 
        error: error.message 
      },
      { status: 500 }
    );
  }
}
