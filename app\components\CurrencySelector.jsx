'use client';

import { useState, useEffect } from 'react';
import { FiGlobe } from 'react-icons/fi';
import { useCurrency } from '../lib/currencyContext';

export default function CurrencySelector() {
  const [isOpen, setIsOpen] = useState(false);
  const [isClient, setIsClient] = useState(false);

  // Ensure we're on the client side before using the currency context
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Use try-catch to handle the case when the currency context is not available
  let currencyContextValue = {
    currencies: [
      { code: 'USD', name: 'US Dollar', symbol: '$', rate: 1.00, isDefault: true },
    ],
    currentCurrency: { code: 'USD', name: 'US Dollar', symbol: '$', rate: 1.00, isDefault: true },
    changeCurrency: () => {},
    enableMultipleCurrencies: false
  };

  try {
    currencyContextValue = useCurrency();
  } catch (error) {
    console.error('Currency context not available:', error);
    return null; // Don't render the component if the context is not available
  }

  const { currencies, currentCurrency, changeCurrency, enableMultipleCurrencies } = currencyContextValue;

  // If multiple currencies are disabled, don't show the selector
  if (!enableMultipleCurrencies) {
    return null;
  }

  return (
    <div className="relative">
      <button
        type="button"
        className="flex items-center text-gray-500 hover:text-gray-700 focus:outline-none"
        onClick={() => setIsOpen(!isOpen)}
      >
        <FiGlobe className="h-5 w-5 mr-1" />
        <span className="text-sm font-medium">{currentCurrency.code}</span>
      </button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50">
          <div className="py-1" role="menu" aria-orientation="vertical" aria-labelledby="options-menu">
            {currencies.map((currency) => (
              <button
                key={currency.code}
                className={`block px-4 py-2 text-sm w-full text-left ${
                  currency.code === currentCurrency.code
                    ? 'bg-gray-100 text-gray-900'
                    : 'text-gray-700 hover:bg-gray-50'
                }`}
                role="menuitem"
                onClick={() => {
                  changeCurrency(currency.code);
                  setIsOpen(false);
                }}
              >
                <div className="flex items-center justify-between">
                  <span>{currency.name}</span>
                  <span className="font-medium">{currency.symbol}</span>
                </div>
                {currency.code !== currentCurrency.code && (
                  <div className="text-xs text-gray-500 mt-1">
                    1 {currentCurrency.code} = {(currency.rate / currentCurrency.rate).toFixed(2)} {currency.code}
                  </div>
                )}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
