'use client';

import { Toaster } from 'react-hot-toast';
import Sidebar from './Sidebar';
import Header from './Header';

export default function DashboardLayout({ children, title = 'Dashboard' }) {
  return (
    <div className="flex h-screen bg-gray-50">
      <Sidebar />
      
      <div className="flex-1 flex flex-col md:ml-64">
        <Header title={title} />
        
        <main className="flex-1 overflow-y-auto p-6">
          {children}
        </main>
        
        <footer className="bg-white border-t py-4 px-6">
          <p className="text-center text-gray-500 text-sm">
            &copy; {new Date().getFullYear()} POS System. All rights reserved.
          </p>
        </footer>
      </div>
      
      <Toaster position="top-right" />
    </div>
  );
}
