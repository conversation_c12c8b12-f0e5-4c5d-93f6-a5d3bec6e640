'use client';

import { useState } from 'react';
import { <PERSON><PERSON>ell, FiUser, FiSearch } from 'react-icons/fi';
import CurrencySelector from './CurrencySelector';

export default function Header({ title }) {
  const [searchQuery, setSearchQuery] = useState('');

  return (
    <header className="bg-white border-b px-6 py-4">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-800">{title}</h1>

        <div className="flex items-center space-x-4">
          {/* Search */}
          <div className="relative hidden md:block">
            <input
              type="text"
              placeholder="Search..."
              className="w-64 pl-10 pr-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          </div>

          {/* Currency Selector */}
          <div className="hidden md:block">
            <CurrencySelector />
          </div>

          {/* Notifications */}
          <button className="p-2 rounded-full hover:bg-gray-100 relative">
            <FiBell size={20} />
            <span className="absolute top-0 right-0 h-4 w-4 bg-red-500 rounded-full text-xs text-white flex items-center justify-center">
              3
            </span>
          </button>

          {/* User profile */}
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center">
              <FiUser />
            </div>
            <span className="hidden md:inline text-sm font-medium">Admin User</span>
          </div>
        </div>
      </div>
    </header>
  );
}
