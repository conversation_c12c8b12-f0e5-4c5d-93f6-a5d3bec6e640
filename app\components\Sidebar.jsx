'use client';

import { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { 
  FiHome, 
  FiShoppingCart, 
  FiPackage, 
  FiDollarSign, 
  FiBarChart2, 
  FiSettings, 
  FiLogOut,
  FiMenu,
  FiX
} from 'react-icons/fi';

const menuItems = [
  { name: 'Dashboard', path: '/', icon: <FiHome size={20} /> },
  { name: 'POS', path: '/pos', icon: <FiShoppingCart size={20} /> },
  { name: 'Inventory', path: '/inventory', icon: <FiPackage size={20} /> },
  { name: 'Accounting', path: '/accounting', icon: <FiDollarSign size={20} /> },
  { name: 'Reports', path: '/reports', icon: <FiBarChart2 size={20} /> },
  { name: 'Settings', path: '/settings', icon: <FiSettings size={20} /> },
];

export default function Sidebar() {
  const pathname = usePathname();
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [isMobileOpen, setIsMobileOpen] = useState(false);

  const toggleSidebar = () => {
    setIsCollapsed(!isCollapsed);
  };

  const toggleMobileSidebar = () => {
    setIsMobileOpen(!isMobileOpen);
  };

  return (
    <>
      {/* Mobile menu button */}
      <button 
        className="md:hidden fixed top-4 left-4 z-50 p-2 rounded-md bg-primary text-white"
        onClick={toggleMobileSidebar}
      >
        {isMobileOpen ? <FiX size={24} /> : <FiMenu size={24} />}
      </button>

      {/* Sidebar for mobile */}
      <div className={`md:hidden fixed inset-0 z-40 bg-black bg-opacity-50 transition-opacity duration-300 ${
        isMobileOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'
      }`}>
        <div className={`fixed inset-y-0 left-0 w-64 bg-white shadow-lg transform transition-transform duration-300 ${
          isMobileOpen ? 'translate-x-0' : '-translate-x-full'
        }`}>
          <div className="flex flex-col h-full">
            <div className="flex items-center justify-between p-4 border-b">
              <h1 className="text-xl font-bold text-primary">POS System</h1>
              <button onClick={toggleMobileSidebar}>
                <FiX size={24} />
              </button>
            </div>
            <nav className="flex-1 overflow-y-auto p-4">
              <ul className="space-y-2">
                {menuItems.map((item) => (
                  <li key={item.path}>
                    <Link
                      href={item.path}
                      className={`flex items-center p-3 rounded-lg transition-colors ${
                        pathname === item.path
                          ? 'bg-primary text-white'
                          : 'text-gray-700 hover:bg-gray-100'
                      }`}
                      onClick={() => setIsMobileOpen(false)}
                    >
                      <span className="mr-3">{item.icon}</span>
                      <span>{item.name}</span>
                    </Link>
                  </li>
                ))}
              </ul>
            </nav>
            <div className="p-4 border-t">
              <button className="flex items-center w-full p-3 rounded-lg text-gray-700 hover:bg-gray-100">
                <FiLogOut size={20} className="mr-3" />
                <span>Logout</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Sidebar for desktop */}
      <aside className={`hidden md:flex flex-col h-screen bg-white border-r transition-all duration-300 ${
        isCollapsed ? 'w-20' : 'w-64'
      } fixed`}>
        <div className="flex items-center justify-between p-4 border-b">
          {!isCollapsed && <h1 className="text-xl font-bold text-primary">POS System</h1>}
          <button 
            onClick={toggleSidebar}
            className={`p-2 rounded-md hover:bg-gray-100 ${isCollapsed ? 'mx-auto' : ''}`}
          >
            <FiMenu size={20} />
          </button>
        </div>
        <nav className="flex-1 overflow-y-auto p-4">
          <ul className="space-y-2">
            {menuItems.map((item) => (
              <li key={item.path}>
                <Link
                  href={item.path}
                  className={`flex items-center p-3 rounded-lg transition-colors ${
                    pathname === item.path
                      ? 'bg-primary text-white'
                      : 'text-gray-700 hover:bg-gray-100'
                  } ${isCollapsed ? 'justify-center' : ''}`}
                >
                  <span className={isCollapsed ? '' : 'mr-3'}>{item.icon}</span>
                  {!isCollapsed && <span>{item.name}</span>}
                </Link>
              </li>
            ))}
          </ul>
        </nav>
        <div className="p-4 border-t">
          <button className={`flex items-center p-3 rounded-lg text-gray-700 hover:bg-gray-100 ${
            isCollapsed ? 'justify-center w-full' : 'w-full'
          }`}>
            <FiLogOut size={20} className={isCollapsed ? '' : 'mr-3'} />
            {!isCollapsed && <span>Logout</span>}
          </button>
        </div>
      </aside>
    </>
  );
}
