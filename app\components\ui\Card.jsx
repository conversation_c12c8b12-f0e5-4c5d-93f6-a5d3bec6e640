'use client';

export default function Card({
  children,
  title,
  subtitle,
  footer,
  className = '',
  ...props
}) {
  return (
    <div
      className={`bg-white rounded-lg shadow-md overflow-hidden border border-gray-100 ${className}`}
      {...props}
    >
      {(title || subtitle) && (
        <div className="px-6 py-5 border-b">
          {title && <h3 className="text-lg font-semibold text-gray-900">{title}</h3>}
          {subtitle && <p className="mt-1 text-sm text-gray-500">{subtitle}</p>}
        </div>
      )}

      <div className="p-6">
        {children}
      </div>

      {footer && (
        <div className="px-6 py-5 bg-gray-50 border-t">
          {footer}
        </div>
      )}
    </div>
  );
}
