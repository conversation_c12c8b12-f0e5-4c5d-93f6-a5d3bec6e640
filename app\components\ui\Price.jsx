'use client';

import { useState, useEffect } from 'react';
import { useCurrency } from '../../lib/currencyContext';

export default function Price({ amount, currencyCode = null }) {
  const [isClient, setIsClient] = useState(false);
  const [formattedPrice, setFormattedPrice] = useState('');

  // Always call the currency hook, but handle errors gracefully
  let formatPrice = null;
  try {
    const currencyContext = useCurrency();
    formatPrice = currencyContext?.formatPrice;
  } catch (error) {
    // Currency context not available
    formatPrice = null;
  }

  // Ensure we're on the client side
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Update formatted price when client state or dependencies change
  useEffect(() => {
    if (isClient && formatPrice) {
      try {
        setFormattedPrice(formatPrice(amount, currencyCode));
      } catch (error) {
        setFormattedPrice(`₨${Math.round(amount).toLocaleString()}`);
      }
    } else {
      setFormattedPrice(`₨${Math.round(amount).toLocaleString()}`);
    }
  }, [isClient, amount, currencyCode, formatPrice]);

  return <>{formattedPrice}</>;
}
