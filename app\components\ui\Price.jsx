'use client';

import { useState, useEffect } from 'react';
import { useCurrency } from '../../lib/currencyContext';

export default function Price({ amount, currencyCode = null }) {
  const [isClient, setIsClient] = useState(false);

  // Ensure we're on the client side before using the currency context
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Show a simple fallback during SSR with PKR
  if (!isClient) {
    return <>₨{Math.round(amount).toLocaleString()}</>;
  }

  // Use try-catch to handle the case when the currency context is not available
  try {
    const { formatPrice } = useCurrency();
    return <>{formatPrice(amount, currencyCode)}</>;
  } catch (error) {
    // Fallback to PKR if currency context is not available
    return <>₨{Math.round(amount).toLocaleString()}</>;
  }
}
