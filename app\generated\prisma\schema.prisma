generator client {
  provider = "prisma-client-js"
  output   = "../app/generated/prisma"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model User {
  id        Int      @id @default(autoincrement())
  name      String
  email     String   @unique
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@map("users")
}

model products {
  id               Int     @id
  category_name    String? @db.VarChar(255)
  subcategory      String? @db.VarChar(255)
  product_name     String? @db.VarChar(255)
  sku              BigInt?
  image_url        String? @db.VarChar(512)
  price            Int?
  supplier_id      String? @db.VarChar(255)
  buying_price     Int?
  product_quantity String? @db.VarChar(255)
  product_url      String? @db.VarChar(512)
  brand            String? @db.VarChar(255)
}

model Admin {
  id        Int      @id @default(autoincrement())
  name      String
  email     String   @unique
  password  String
  role      String   @default("admin")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@map("admins")
}

model Employee {
  id        Int      @id @default(autoincrement())
  name      String
  email     String   @unique
  password  String
  position  String
  status    String   @default("active")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@map("employees")
}
