'use server';

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL || "mysql://admin@localhost:3306/pos"
    }
  }
});

// ==================== INVENTORY MANAGEMENT FUNCTIONS ====================

// Get all products with advanced filtering and pagination for inventory
export async function getInventoryProducts(filters = {}) {
  try {
    const where = {};

    // Apply filters
    if (filters.category && filters.category !== 'all') {
      where.categoryId = parseInt(filters.category);
    }

    if (filters.subcategory && filters.subcategory !== 'all') {
      where.subCategoryId = parseInt(filters.subcategory);
    }

    if (filters.supplier && filters.supplier !== 'all') {
      where.supplierId = parseInt(filters.supplier);
    }

    if (filters.status && filters.status !== 'all') {
      if (filters.status === 'active') {
        where.is_active = true;
      } else if (filters.status === 'inactive') {
        where.is_active = false;
      } else if (filters.status === 'low_stock') {
        where.product_quantity = { lte: parseInt(filters.lowStockThreshold) || 10 };
        where.is_active = true;
      } else if (filters.status === 'out_of_stock') {
        where.product_quantity = { lte: 0 };
        where.is_active = true;
      }
    }

    if (filters.search) {
      where.OR = [
        { name: { contains: filters.search } },
        { sku: { contains: filters.search } },
        { brand: { contains: filters.search } },
        { description: { contains: filters.search } }
      ];
    }

    if (filters.priceMin) {
      where.price = { ...where.price, gte: parseFloat(filters.priceMin) };
    }

    if (filters.priceMax) {
      where.price = { ...where.price, lte: parseFloat(filters.priceMax) };
    }

    // Pagination parameters
    const page = parseInt(filters.page) || 1;
    const limit = parseInt(filters.limit) || 50;
    const skip = (page - 1) * limit;

    // Sorting
    let orderBy = { name: 'asc' };
    if (filters.sortBy) {
      switch (filters.sortBy) {
        case 'name_asc':
          orderBy = { name: 'asc' };
          break;
        case 'name_desc':
          orderBy = { name: 'desc' };
          break;
        case 'price_asc':
          orderBy = { price: 'asc' };
          break;
        case 'price_desc':
          orderBy = { price: 'desc' };
          break;
        case 'stock_asc':
          orderBy = { product_quantity: 'asc' };
          break;
        case 'stock_desc':
          orderBy = { product_quantity: 'desc' };
          break;
        case 'created_asc':
          orderBy = { createdAt: 'asc' };
          break;
        case 'created_desc':
          orderBy = { createdAt: 'desc' };
          break;
        default:
          orderBy = { name: 'asc' };
      }
    }

    // Get total count for pagination
    const totalCount = await prisma.product.count({ where });

    const products = await prisma.product.findMany({
      where,
      include: {
        category: true,
        subCategory: true,
        supplier: true,
      },
      orderBy,
      skip,
      take: limit,
    });

    // Calculate inventory statistics
    const stats = await getInventoryStats(where);

    return {
      products,
      stats,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        hasNext: page < Math.ceil(totalCount / limit),
        hasPrev: page > 1,
      }
    };
  } catch (error) {
    console.error('Error fetching inventory products:', error);
    return {
      products: [],
      stats: {},
      pagination: {
        page: 1,
        limit: 50,
        totalCount: 0,
        totalPages: 0,
        hasNext: false,
        hasPrev: false,
      }
    };
  }
}

// Get inventory statistics
export async function getInventoryStats(whereClause = {}) {
  try {
    const baseWhere = { is_active: true, ...whereClause };

    const [
      totalProducts,
      totalValue,
      lowStockCount,
      outOfStockCount,
      activeProducts,
      inactiveProducts
    ] = await Promise.all([
      // Total products count
      prisma.product.count({ where: baseWhere }),

      // Total inventory value
      prisma.product.aggregate({
        where: baseWhere,
        _sum: {
          price: true,
          buying_price: true,
        }
      }),

      // Low stock count (≤ 10)
      prisma.product.count({
        where: {
          ...baseWhere,
          product_quantity: { lte: 10, gt: 0 }
        }
      }),

      // Out of stock count
      prisma.product.count({
        where: {
          ...baseWhere,
          product_quantity: { lte: 0 }
        }
      }),

      // Active products
      prisma.product.count({
        where: { is_active: true }
      }),

      // Inactive products
      prisma.product.count({
        where: { is_active: false }
      })
    ]);

    // Calculate total stock quantity
    const stockData = await prisma.product.aggregate({
      where: baseWhere,
      _sum: {
        product_quantity: true
      }
    });

    return {
      totalProducts,
      totalStockQuantity: stockData._sum.product_quantity || 0,
      totalSellingValue: totalValue._sum.price || 0,
      totalBuyingValue: totalValue._sum.buying_price || 0,
      lowStockCount,
      outOfStockCount,
      activeProducts,
      inactiveProducts,
      profitMargin: totalValue._sum.price && totalValue._sum.buying_price
        ? ((totalValue._sum.price - totalValue._sum.buying_price) / totalValue._sum.price * 100).toFixed(2)
        : 0
    };
  } catch (error) {
    console.error('Error fetching inventory stats:', error);
    return {
      totalProducts: 0,
      totalStockQuantity: 0,
      totalSellingValue: 0,
      totalBuyingValue: 0,
      lowStockCount: 0,
      outOfStockCount: 0,
      activeProducts: 0,
      inactiveProducts: 0,
      profitMargin: 0
    };
  }
}

// Get all categories for filters
export async function getCategories() {
  try {
    const categories = await prisma.category.findMany({
      orderBy: { name: 'asc' }
    });
    return categories;
  } catch (error) {
    console.error('Error fetching categories:', error);
    return [];
  }
}

// Get subcategories by category
export async function getSubCategories(categoryId = null) {
  try {
    const where = categoryId ? { categoryId: parseInt(categoryId) } : {};
    const subCategories = await prisma.subCategory.findMany({
      where,
      include: {
        category: true
      },
      orderBy: { name: 'asc' }
    });
    return subCategories;
  } catch (error) {
    console.error('Error fetching subcategories:', error);
    return [];
  }
}

// Get all suppliers for filters
export async function getSuppliers() {
  try {
    const suppliers = await prisma.supplier.findMany({
      orderBy: { name: 'asc' }
    });
    return suppliers;
  } catch (error) {
    console.error('Error fetching suppliers:', error);
    return [];
  }
}

// Create new product
export async function createProduct(productData) {
  try {
    // Validate required fields
    if (!productData.name || !productData.sku || !productData.price) {
      throw new Error('Name, SKU, and price are required');
    }

    // Check if SKU already exists
    const existingSku = await prisma.product.findUnique({
      where: { sku: productData.sku }
    });

    if (existingSku) {
      throw new Error('Product with this SKU already exists');
    }

    const product = await prisma.product.create({
      data: {
        sku: productData.sku.trim(),
        name: productData.name.trim(),
        description: productData.description?.trim() || null,
        categoryId: productData.categoryId ? parseInt(productData.categoryId) : null,
        subCategoryId: productData.subCategoryId ? parseInt(productData.subCategoryId) : null,
        brand: productData.brand?.trim() || null,
        price: parseFloat(productData.price),
        buying_price: parseFloat(productData.buying_price) || 0,
        image_url: productData.image_url?.trim() || null,
        product_url: productData.product_url?.trim() || null,
        product_quantity: parseInt(productData.product_quantity) || 0,
        is_active: productData.is_active !== undefined ? productData.is_active : true,
        featured: productData.featured !== undefined ? productData.featured : false,
        sortOrder: parseInt(productData.sortOrder) || 0,
        supplierId: productData.supplierId ? parseInt(productData.supplierId) : null,
      },
      include: {
        category: true,
        subCategory: true,
        supplier: true,
      }
    });

    return { success: true, product };
  } catch (error) {
    console.error('Error creating product:', error);
    return {
      success: false,
      error: error.message || 'Failed to create product'
    };
  }
}

// Update product
export async function updateProduct(id, productData) {
  try {
    // Check if SKU already exists (excluding current product)
    if (productData.sku) {
      const existingSku = await prisma.product.findUnique({
        where: { sku: productData.sku }
      });

      if (existingSku && existingSku.id !== parseInt(id)) {
        throw new Error('Product with this SKU already exists');
      }
    }

    const updateData = {};

    // Only update provided fields
    if (productData.sku !== undefined) updateData.sku = productData.sku.trim();
    if (productData.name !== undefined) updateData.name = productData.name.trim();
    if (productData.description !== undefined) updateData.description = productData.description?.trim() || null;
    if (productData.categoryId !== undefined) updateData.categoryId = productData.categoryId ? parseInt(productData.categoryId) : null;
    if (productData.subCategoryId !== undefined) updateData.subCategoryId = productData.subCategoryId ? parseInt(productData.subCategoryId) : null;
    if (productData.brand !== undefined) updateData.brand = productData.brand?.trim() || null;
    if (productData.price !== undefined) updateData.price = parseFloat(productData.price);
    if (productData.buying_price !== undefined) updateData.buying_price = parseFloat(productData.buying_price);
    if (productData.image_url !== undefined) updateData.image_url = productData.image_url?.trim() || null;
    if (productData.product_url !== undefined) updateData.product_url = productData.product_url?.trim() || null;
    if (productData.product_quantity !== undefined) updateData.product_quantity = parseInt(productData.product_quantity);
    if (productData.is_active !== undefined) updateData.is_active = productData.is_active;
    if (productData.featured !== undefined) updateData.featured = productData.featured;
    if (productData.sortOrder !== undefined) updateData.sortOrder = parseInt(productData.sortOrder);
    if (productData.supplierId !== undefined) updateData.supplierId = productData.supplierId ? parseInt(productData.supplierId) : null;

    const product = await prisma.product.update({
      where: { id: parseInt(id) },
      data: updateData,
      include: {
        category: true,
        subCategory: true,
        supplier: true,
      }
    });

    return { success: true, product };
  } catch (error) {
    console.error('Error updating product:', error);
    return {
      success: false,
      error: error.message || 'Failed to update product'
    };
  }
}

// Delete product
export async function deleteProduct(id) {
  try {
    // Check if product is used in any orders, loans, or rentals
    const [orderItems, loanItems, rentalItems] = await Promise.all([
      prisma.orderitem.findFirst({ where: { productId: parseInt(id) } }),
      prisma.loanitem.findFirst({ where: { productId: parseInt(id) } }),
      prisma.rentalitem.findFirst({ where: { productId: parseInt(id) } })
    ]);

    if (orderItems || loanItems || rentalItems) {
      // Instead of deleting, deactivate the product
      const product = await prisma.product.update({
        where: { id: parseInt(id) },
        data: { is_active: false },
        include: {
          category: true,
          subCategory: true,
          supplier: true,
        }
      });

      return {
        success: true,
        product,
        message: 'Product deactivated instead of deleted due to existing transactions'
      };
    }

    await prisma.product.delete({
      where: { id: parseInt(id) }
    });

    return { success: true, message: 'Product deleted successfully' };
  } catch (error) {
    console.error('Error deleting product:', error);
    return {
      success: false,
      error: error.message || 'Failed to delete product'
    };
  }
}

// Get product by ID with full details
export async function getProductById(id) {
  try {
    const product = await prisma.product.findUnique({
      where: { id: parseInt(id) },
      include: {
        category: true,
        subCategory: true,
        supplier: true,
      }
    });

    if (!product) {
      return {
        success: false,
        error: 'Product not found'
      };
    }

    return { success: true, product };
  } catch (error) {
    console.error('Error fetching product:', error);
    return {
      success: false,
      error: error.message || 'Failed to fetch product'
    };
  }
}

// Bulk update products
export async function bulkUpdateProducts(updates) {
  try {
    const results = [];

    for (const update of updates) {
      const { id, ...updateData } = update;
      const result = await updateProduct(id, updateData);
      results.push({ id, ...result });
    }

    const successCount = results.filter(r => r.success).length;
    const failureCount = results.filter(r => !r.success).length;

    return {
      success: true,
      results,
      summary: {
        total: updates.length,
        successful: successCount,
        failed: failureCount
      }
    };
  } catch (error) {
    console.error('Error bulk updating products:', error);
    return {
      success: false,
      error: error.message || 'Failed to bulk update products'
    };
  }
}

// Bulk delete products
export async function bulkDeleteProducts(productIds) {
  try {
    const results = [];

    for (const id of productIds) {
      const result = await deleteProduct(id);
      results.push({ id, ...result });
    }

    const successCount = results.filter(r => r.success).length;
    const failureCount = results.filter(r => !r.success).length;

    return {
      success: true,
      results,
      summary: {
        total: productIds.length,
        successful: successCount,
        failed: failureCount
      }
    };
  } catch (error) {
    console.error('Error bulk deleting products:', error);
    return {
      success: false,
      error: error.message || 'Failed to bulk delete products'
    };
  }
}

// Stock adjustment
export async function adjustStock(productId, adjustmentData) {
  try {
    const { quantity, type, reason, notes } = adjustmentData;

    const product = await prisma.product.findUnique({
      where: { id: parseInt(productId) }
    });

    if (!product) {
      throw new Error('Product not found');
    }

    let newQuantity;
    if (type === 'increase') {
      newQuantity = product.product_quantity + parseInt(quantity);
    } else if (type === 'decrease') {
      newQuantity = Math.max(0, product.product_quantity - parseInt(quantity));
    } else if (type === 'set') {
      newQuantity = parseInt(quantity);
    } else {
      throw new Error('Invalid adjustment type');
    }

    const updatedProduct = await prisma.product.update({
      where: { id: parseInt(productId) },
      data: { product_quantity: newQuantity },
      include: {
        category: true,
        subCategory: true,
        supplier: true,
      }
    });

    // Log the stock adjustment (you might want to create a stock_adjustments table)
    console.log(`Stock adjustment: Product ${productId}, ${type} ${quantity}, Reason: ${reason}, Notes: ${notes}`);

    return {
      success: true,
      product: updatedProduct,
      adjustment: {
        previousQuantity: product.product_quantity,
        newQuantity,
        adjustmentQuantity: quantity,
        type,
        reason,
        notes
      }
    };
  } catch (error) {
    console.error('Error adjusting stock:', error);
    return {
      success: false,
      error: error.message || 'Failed to adjust stock'
    };
  }
}

// Get low stock products
export async function getLowStockProducts(threshold = 10) {
  try {
    const products = await prisma.product.findMany({
      where: {
        is_active: true,
        product_quantity: { lte: threshold, gt: 0 }
      },
      include: {
        category: true,
        subCategory: true,
        supplier: true,
      },
      orderBy: {
        product_quantity: 'asc'
      }
    });

    return { success: true, products };
  } catch (error) {
    console.error('Error fetching low stock products:', error);
    return {
      success: false,
      error: error.message || 'Failed to fetch low stock products',
      products: []
    };
  }
}

// Get out of stock products
export async function getOutOfStockProducts() {
  try {
    const products = await prisma.product.findMany({
      where: {
        is_active: true,
        product_quantity: { lte: 0 }
      },
      include: {
        category: true,
        subCategory: true,
        supplier: true,
      },
      orderBy: {
        name: 'asc'
      }
    });

    return { success: true, products };
  } catch (error) {
    console.error('Error fetching out of stock products:', error);
    return {
      success: false,
      error: error.message || 'Failed to fetch out of stock products',
      products: []
    };
  }
}

// Generate inventory report
export async function generateInventoryReport(filters = {}) {
  try {
    const { reportType = 'summary' } = filters;

    const report = {
      generatedAt: new Date(),
      reportType,
      filters,
      data: {}
    };

    switch (reportType) {
      case 'summary':
        report.data = await getInventoryStats();
        break;

      case 'low_stock':
        const lowStockResult = await getLowStockProducts(filters.threshold || 10);
        report.data = {
          threshold: filters.threshold || 10,
          products: lowStockResult.products || [],
          count: lowStockResult.products?.length || 0
        };
        break;

      case 'out_of_stock':
        const outOfStockResult = await getOutOfStockProducts();
        report.data = {
          products: outOfStockResult.products || [],
          count: outOfStockResult.products?.length || 0
        };
        break;

      case 'valuation':
        const valuationData = await prisma.product.findMany({
          where: { is_active: true },
          select: {
            id: true,
            name: true,
            sku: true,
            product_quantity: true,
            price: true,
            buying_price: true,
            category: { select: { name: true } },
            subCategory: { select: { name: true } }
          }
        });

        const totalSellingValue = valuationData.reduce((sum, p) => sum + (p.price * p.product_quantity), 0);
        const totalBuyingValue = valuationData.reduce((sum, p) => sum + (p.buying_price * p.product_quantity), 0);

        report.data = {
          products: valuationData,
          summary: {
            totalProducts: valuationData.length,
            totalSellingValue,
            totalBuyingValue,
            potentialProfit: totalSellingValue - totalBuyingValue,
            profitMargin: totalSellingValue > 0 ? ((totalSellingValue - totalBuyingValue) / totalSellingValue * 100).toFixed(2) : 0
          }
        };
        break;

      default:
        throw new Error('Invalid report type');
    }

    return { success: true, report };
  } catch (error) {
    console.error('Error generating inventory report:', error);
    return {
      success: false,
      error: error.message || 'Failed to generate inventory report'
    };
  }
}

// Import products from CSV/Excel data
export async function importProducts(productsData) {
  try {
    const results = [];
    const errors = [];

    for (let i = 0; i < productsData.length; i++) {
      const productData = productsData[i];

      try {
        // Validate required fields
        if (!productData.name || !productData.sku || !productData.price) {
          errors.push({
            row: i + 1,
            error: 'Missing required fields: name, sku, or price'
          });
          continue;
        }

        // Check if SKU already exists
        const existingSku = await prisma.product.findUnique({
          where: { sku: productData.sku }
        });

        if (existingSku) {
          errors.push({
            row: i + 1,
            sku: productData.sku,
            error: 'SKU already exists'
          });
          continue;
        }

        const result = await createProduct(productData);

        if (result.success) {
          results.push({
            row: i + 1,
            sku: productData.sku,
            name: productData.name,
            status: 'success'
          });
        } else {
          errors.push({
            row: i + 1,
            sku: productData.sku,
            error: result.error
          });
        }
      } catch (error) {
        errors.push({
          row: i + 1,
          sku: productData.sku || 'Unknown',
          error: error.message
        });
      }
    }

    return {
      success: true,
      summary: {
        total: productsData.length,
        successful: results.length,
        failed: errors.length
      },
      results,
      errors
    };
  } catch (error) {
    console.error('Error importing products:', error);
    return {
      success: false,
      error: error.message || 'Failed to import products'
    };
  }
}

// Export products to CSV format
export async function exportProducts(filters = {}) {
  try {
    const { products } = await getInventoryProducts({
      ...filters,
      limit: 10000 // Get all products for export
    });

    const csvData = products.map(product => ({
      SKU: product.sku,
      Name: product.name,
      Description: product.description || '',
      Category: product.category?.name || '',
      SubCategory: product.subCategory?.name || '',
      Brand: product.brand || '',
      Price: product.price,
      BuyingPrice: product.buying_price,
      Quantity: product.product_quantity,
      Status: product.is_active ? 'Active' : 'Inactive',
      Featured: product.featured ? 'Yes' : 'No',
      Supplier: product.supplier?.name || '',
      CreatedAt: product.createdAt,
      UpdatedAt: product.updatedAt
    }));

    return {
      success: true,
      data: csvData,
      filename: `inventory_export_${new Date().toISOString().split('T')[0]}.csv`
    };
  } catch (error) {
    console.error('Error exporting products:', error);
    return {
      success: false,
      error: error.message || 'Failed to export products'
    };
  }
}

// Get product by SKU
export async function getProductBySku(sku) {
  try {
    const product = await prisma.product.findUnique({
      where: { sku: sku.trim() },
      include: {
        category: true,
        subCategory: true,
        supplier: true,
      }
    });

    if (!product) {
      return {
        success: false,
        error: 'Product not found'
      };
    }

    return { success: true, product };
  } catch (error) {
    console.error('Error fetching product by SKU:', error);
    return {
      success: false,
      error: error.message || 'Failed to fetch product'
    };
  }
}

// Duplicate product
export async function duplicateProduct(id) {
  try {
    const originalProduct = await prisma.product.findUnique({
      where: { id: parseInt(id) }
    });

    if (!originalProduct) {
      throw new Error('Product not found');
    }

    // Generate new SKU
    const timestamp = Date.now().toString().slice(-6);
    const newSku = `${originalProduct.sku}-COPY-${timestamp}`;

    const duplicatedProduct = await prisma.product.create({
      data: {
        sku: newSku,
        name: `${originalProduct.name} (Copy)`,
        description: originalProduct.description,
        categoryId: originalProduct.categoryId,
        subCategoryId: originalProduct.subCategoryId,
        brand: originalProduct.brand,
        price: originalProduct.price,
        buying_price: originalProduct.buying_price,
        image_url: originalProduct.image_url,
        product_url: originalProduct.product_url,
        product_quantity: 0, // Start with 0 quantity for duplicated product
        is_active: false, // Start as inactive
        featured: false,
        sortOrder: originalProduct.sortOrder,
        supplierId: originalProduct.supplierId,
      },
      include: {
        category: true,
        subCategory: true,
        supplier: true,
      }
    });

    return { success: true, product: duplicatedProduct };
  } catch (error) {
    console.error('Error duplicating product:', error);
    return {
      success: false,
      error: error.message || 'Failed to duplicate product'
    };
  }
}
