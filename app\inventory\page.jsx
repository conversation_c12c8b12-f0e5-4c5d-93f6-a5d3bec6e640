'use client';

import { useState, useEffect } from 'react';
import { FiPlus, FiEdit2, FiTrash2, FiFilter, FiDownload, FiUpload } from 'react-icons/fi';
import DashboardLayout from '../components/DashboardLayout';
import Card from '../components/ui/Card';
import Button from '../components/ui/Button';
import Table from '../components/ui/Table';
import Modal from '../components/ui/Modal';
import { Input, Select, Textarea, FormGroup, FormRow } from '../components/ui/Form';
import toast from 'react-hot-toast';


export default function InventoryPage() {
  const [products, setProducts] = useState([]);
  const [categories, setCategories] = useState([]);
  const [suppliers, setSuppliers] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isProductModalOpen, setIsProductModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [currentProduct, setCurrentProduct] = useState(null);
  const [formData, setFormData] = useState({
    sku: '',
    name: '',
    description: '',
    categoryId: '',
    supplierId: '',
    price: '',
    buying_price: '',
    product_quantity: '',
    is_active: true,
  });
  const [filters, setFilters] = useState({
    category: '',
    status: '',
    stock: '',
  });

  // Load data
  useEffect(() => {
    const timer = setTimeout(() => {
      setProducts(mockProducts);
      setCategories(mockCategories);
      setSuppliers(mockSuppliers);
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value,
    });
  };

  // Open modal to add new product
  const handleAddProduct = () => {
    setCurrentProduct(null);
    setFormData({
      sku: '',
      name: '',
      description: '',
      categoryId: '',
      supplierId: '',
      price: '',
      buying_price: '',
      product_quantity: '',
      is_active: true,
    });
    setIsProductModalOpen(true);
  };

  // Open modal to edit product
  const handleEditProduct = (product) => {
    setCurrentProduct(product);
    setFormData({
      sku: product.sku,
      name: product.name,
      description: product.description || '',
      categoryId: categories.find(c => c.name === product.category)?.id || '',
      supplierId: '',
      price: product.price.toString(),
      buying_price: product.buying_price.toString(),
      product_quantity: product.product_quantity.toString(),
      is_active: product.is_active,
    });
    setIsProductModalOpen(true);
  };

  // Open delete confirmation modal
  const handleDeleteClick = (product) => {
    setCurrentProduct(product);
    setIsDeleteModalOpen(true);
  };

  // Save product (add or update)
  const handleSaveProduct = () => {
    // Validate form
    if (!formData.sku || !formData.name || !formData.price || !formData.buying_price) {
      toast.error('Please fill in all required fields');
      return;
    }

    // Here you would normally save to the database
    if (currentProduct) {
      // Update existing product
      setProducts(products.map(p => 
        p.id === currentProduct.id 
          ? { 
              ...p, 
              sku: formData.sku,
              name: formData.name,
              description: formData.description,
              category: categories.find(c => c.id === parseInt(formData.categoryId))?.name || p.category,
              price: parseFloat(formData.price),
              buying_price: parseFloat(formData.buying_price),
              product_quantity: parseInt(formData.product_quantity),
              is_active: formData.is_active,
            } 
          : p
      ));
      toast.success('Product updated successfully');
    } else {
      // Add new product
      const newProduct = {
        id: Math.max(...products.map(p => p.id)) + 1,
        sku: formData.sku,
        name: formData.name,
        description: formData.description,
        category: categories.find(c => c.id === parseInt(formData.categoryId))?.name || 'Uncategorized',
        price: parseFloat(formData.price),
        buying_price: parseFloat(formData.buying_price),
        product_quantity: parseInt(formData.product_quantity),
        is_active: formData.is_active,
      };
      setProducts([...products, newProduct]);
      toast.success('Product added successfully');
    }

    setIsProductModalOpen(false);
  };

  // Delete product
  const handleDeleteProduct = () => {
    if (!currentProduct) return;
    
    // Here you would normally delete from the database
    setProducts(products.filter(p => p.id !== currentProduct.id));
    toast.success('Product deleted successfully');
    setIsDeleteModalOpen(false);
  };

  // Apply filters
  const filteredProducts = products.filter(product => {
    if (filters.category && product.category !== filters.category) return false;
    if (filters.status === 'active' && !product.is_active) return false;
    if (filters.status === 'inactive' && product.is_active) return false;
    if (filters.stock === 'low' && product.product_quantity > 10) return false;
    if (filters.stock === 'out' && product.product_quantity > 0) return false;
    return true;
  });

  // Table columns
  const columns = [
    { key: 'sku', label: 'SKU' },
    { key: 'name', label: 'Name' },
    { key: 'category', label: 'Category' },
    { 
      key: 'price', 
      label: 'Price',
      render: (row) => `$${row.price.toFixed(2)}`,
    },
    { 
      key: 'buying_price', 
      label: 'Cost',
      render: (row) => `$${row.buying_price.toFixed(2)}`,
    },
    { 
      key: 'product_quantity', 
      label: 'Stock',
      render: (row) => {
        if (row.product_quantity <= 0) {
          return <span className="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800">Out of Stock</span>;
        } else if (row.product_quantity <= 10) {
          return <span className="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800">{row.product_quantity} (Low)</span>;
        } else {
          return row.product_quantity;
        }
      },
    },
    { 
      key: 'is_active', 
      label: 'Status',
      render: (row) => (
        <span className={`px-2 py-1 text-xs rounded-full ${
          row.is_active 
            ? 'bg-green-100 text-green-800' 
            : 'bg-gray-100 text-gray-800'
        }`}>
          {row.is_active ? 'Active' : 'Inactive'}
        </span>
      ),
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (row) => (
        <div className="flex space-x-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={(e) => {
              e.stopPropagation();
              handleEditProduct(row);
            }}
          >
            <FiEdit2 size={16} />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            className="text-red-500"
            onClick={(e) => {
              e.stopPropagation();
              handleDeleteClick(row);
            }}
          >
            <FiTrash2 size={16} />
          </Button>
        </div>
      ),
    },
  ];

  return (
    <DashboardLayout title="Inventory Management">
      <Card>
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6 gap-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <Select
              placeholder="Filter by Category"
              value={filters.category}
              onChange={(e) => setFilters({ ...filters, category: e.target.value })}
              options={[
                { value: '', label: 'All Categories' },
                ...categories.map(c => ({ value: c.name, label: c.name })),
              ]}
              className="w-full sm:w-40"
            />
            <Select
              placeholder="Filter by Status"
              value={filters.status}
              onChange={(e) => setFilters({ ...filters, status: e.target.value })}
              options={[
                { value: '', label: 'All Status' },
                { value: 'active', label: 'Active' },
                { value: 'inactive', label: 'Inactive' },
              ]}
              className="w-full sm:w-40"
            />
            <Select
              placeholder="Filter by Stock"
              value={filters.stock}
              onChange={(e) => setFilters({ ...filters, stock: e.target.value })}
              options={[
                { value: '', label: 'All Stock' },
                { value: 'low', label: 'Low Stock' },
                { value: 'out', label: 'Out of Stock' },
              ]}
              className="w-full sm:w-40"
            />
          </div>
          
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => setFilters({ category: '', status: '', stock: '' })}
            >
              <FiFilter className="mr-1" /> Clear Filters
            </Button>
            <Button
              variant="outline"
            >
              <FiDownload className="mr-1" /> Export
            </Button>
            <Button
              variant="outline"
            >
              <FiUpload className="mr-1" /> Import
            </Button>
            <Button
              onClick={handleAddProduct}
            >
              <FiPlus className="mr-1" /> Add Product
            </Button>
          </div>
        </div>
        
        {isLoading ? (
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
          </div>
        ) : (
          <Table
            columns={columns}
            data={filteredProducts}
            pagination={true}
            itemsPerPage={10}
            onRowClick={handleEditProduct}
          />
        )}
      </Card>
      
      {/* Product Modal (Add/Edit) */}
      <Modal
        isOpen={isProductModalOpen}
        onClose={() => setIsProductModalOpen(false)}
        title={currentProduct ? 'Edit Product' : 'Add New Product'}
        size="lg"
        footer={
          <>
            <Button
              variant="outline"
              onClick={() => setIsProductModalOpen(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSaveProduct}
            >
              {currentProduct ? 'Update Product' : 'Add Product'}
            </Button>
          </>
        }
      >
        <FormGroup>
          <FormRow>
            <Input
              label="SKU"
              name="sku"
              value={formData.sku}
              onChange={handleInputChange}
              required
            />
            <Input
              label="Name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              required
            />
          </FormRow>
          
          <Textarea
            label="Description"
            name="description"
            value={formData.description}
            onChange={handleInputChange}
          />
          
          <FormRow>
            <Select
              label="Category"
              name="categoryId"
              value={formData.categoryId}
              onChange={handleInputChange}
              options={categories.map(c => ({ value: c.id, label: c.name }))}
              required
            />
            <Select
              label="Supplier"
              name="supplierId"
              value={formData.supplierId}
              onChange={handleInputChange}
              options={suppliers.map(s => ({ value: s.id, label: s.name }))}
            />
          </FormRow>
          
          <FormRow>
            <Input
              label="Selling Price"
              name="price"
              type="number"
              min="0"
              step="0.01"
              value={formData.price}
              onChange={handleInputChange}
              required
            />
            <Input
              label="Cost Price"
              name="buying_price"
              type="number"
              min="0"
              step="0.01"
              value={formData.buying_price}
              onChange={handleInputChange}
              required
            />
          </FormRow>
          
          <FormRow>
            <Input
              label="Stock Quantity"
              name="product_quantity"
              type="number"
              min="0"
              value={formData.product_quantity}
              onChange={handleInputChange}
              required
            />
            <div className="flex items-center h-full pt-6">
              <input
                type="checkbox"
                id="is_active"
                name="is_active"
                checked={formData.is_active}
                onChange={handleInputChange}
                className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
              />
              <label htmlFor="is_active" className="ml-2 block text-sm text-gray-900">
                Active
              </label>
            </div>
          </FormRow>
        </FormGroup>
      </Modal>
      
      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        title="Delete Product"
        size="sm"
        footer={
          <>
            <Button
              variant="outline"
              onClick={() => setIsDeleteModalOpen(false)}
            >
              Cancel
            </Button>
            <Button
              variant="danger"
              onClick={handleDeleteProduct}
            >
              Delete
            </Button>
          </>
        }
      >
        <p>Are you sure you want to delete <strong>{currentProduct?.name}</strong>?</p>
        <p className="text-sm text-gray-500 mt-2">This action cannot be undone.</p>
      </Modal>
    </DashboardLayout>
  );
}
