'use client';

import { createContext, useContext, useState, useEffect } from 'react';

// Default currency data
const defaultCurrencies = [
  { code: 'PKR', name: 'Pakistani Rupee', symbol: '₨', rate: 1.00, isDefault: true },
  { code: 'USD', name: 'US Dollar', symbol: '$', rate: 0.0036, isDefault: false },
  { code: 'EUR', name: 'Euro', symbol: '€', rate: 0.0031, isDefault: false },
];

// Create the context
const CurrencyContext = createContext();

// Currency provider component
export function CurrencyProvider({ children }) {
  // State for the current currency and all available currencies
  const [currencies, setCurrencies] = useState(defaultCurrencies);
  const [currentCurrency, setCurrentCurrency] = useState(defaultCurrencies.find(c => c.isDefault));
  const [enableMultipleCurrencies, setEnableMultipleCurrencies] = useState(true);

  // Load currency settings from localStorage on component mount
  useEffect(() => {
    // Check if we're in the browser environment
    if (typeof window !== 'undefined') {
      try {
        const storedCurrencies = localStorage.getItem('currencies');
        const storedCurrentCurrency = localStorage.getItem('currentCurrency');
        const storedEnableMultipleCurrencies = localStorage.getItem('enableMultipleCurrencies');

        if (storedCurrencies) {
          setCurrencies(JSON.parse(storedCurrencies));
        }

        if (storedCurrentCurrency) {
          setCurrentCurrency(JSON.parse(storedCurrentCurrency));
        }

        if (storedEnableMultipleCurrencies !== null) {
          setEnableMultipleCurrencies(JSON.parse(storedEnableMultipleCurrencies));
        }
      } catch (error) {
        console.error('Error loading currency settings from localStorage:', error);
      }
    }
  }, []);

  // Save currency settings to localStorage whenever they change
  useEffect(() => {
    // Check if we're in the browser environment
    if (typeof window !== 'undefined') {
      try {
        localStorage.setItem('currencies', JSON.stringify(currencies));
        localStorage.setItem('currentCurrency', JSON.stringify(currentCurrency));
        localStorage.setItem('enableMultipleCurrencies', JSON.stringify(enableMultipleCurrencies));
      } catch (error) {
        console.error('Error saving currency settings to localStorage:', error);
      }
    }
  }, [currencies, currentCurrency, enableMultipleCurrencies]);

  // Function to change the current currency
  const changeCurrency = (currencyCode) => {
    const newCurrency = currencies.find(c => c.code === currencyCode);
    if (newCurrency) {
      setCurrentCurrency(newCurrency);
    }
  };

  // Function to update currency settings
  const updateCurrencySettings = (settings) => {
    if (settings.currencies) {
      setCurrencies(settings.currencies);

      // Update current currency if it's the default one
      const defaultCurrency = settings.currencies.find(c => c.isDefault);
      if (defaultCurrency) {
        setCurrentCurrency(defaultCurrency);
      }
    }

    if (settings.enableMultipleCurrencies !== undefined) {
      setEnableMultipleCurrencies(settings.enableMultipleCurrencies);
    }
  };

  // Function to format a price according to the current currency
  const formatPrice = (amount, currencyCode = null) => {
    try {
      // Use specified currency or current currency
      const currency = currencyCode
        ? currencies.find(c => c.code === currencyCode)
        : currentCurrency;

      if (!currency) {
        throw new Error(`Currency not found: ${currencyCode}`);
      }

      // Convert amount to the target currency
      const defaultCurrency = currencies.find(c => c.isDefault);
      const convertedAmount = defaultCurrency.code === currency.code
        ? amount
        : amount * currency.rate;

      // Format the amount with the currency symbol
      // For PKR, don't show decimal places if it's a whole number
      if (currency.code === 'PKR') {
        const rounded = Math.round(convertedAmount);
        return `${currency.symbol}${rounded.toLocaleString()}`;
      }
      return `${currency.symbol}${convertedAmount.toFixed(2)}`;
    } catch (error) {
      console.error('Error formatting price:', error);
      return `₨${Math.round(amount).toLocaleString()}`;
    }
  };

  // Function to convert a price from one currency to another
  const convertPrice = (amount, fromCurrency, toCurrency) => {
    try {
      const from = currencies.find(c => c.code === fromCurrency);
      const to = currencies.find(c => c.code === toCurrency);

      if (!from || !to) {
        throw new Error(`Currency not found: ${!from ? fromCurrency : toCurrency}`);
      }

      // Convert to the default currency first (if not already)
      const defaultCurrency = currencies.find(c => c.isDefault);
      const amountInDefaultCurrency = from.code === defaultCurrency.code
        ? amount
        : amount / from.rate;

      // Then convert from default currency to target currency
      const convertedAmount = to.code === defaultCurrency.code
        ? amountInDefaultCurrency
        : amountInDefaultCurrency * to.rate;

      return convertedAmount;
    } catch (error) {
      console.error('Error converting price:', error);
      return amount;
    }
  };

  // Context value
  const value = {
    currencies,
    currentCurrency,
    enableMultipleCurrencies,
    changeCurrency,
    updateCurrencySettings,
    formatPrice,
    convertPrice,
  };

  return (
    <CurrencyContext.Provider value={value}>
      {children}
    </CurrencyContext.Provider>
  );
}

// Custom hook to use the currency context
export function useCurrency() {
  const context = useContext(CurrencyContext);
  if (context === undefined) {
    throw new Error('useCurrency must be used within a CurrencyProvider');
  }
  return context;
}

export default CurrencyContext;
