'use server';

import { PrismaClient } from '@prisma/client';

// Initialize Prisma client
const prisma = new PrismaClient();

// Dashboard statistics
export async function getDashboardStats() {
  try {
    // Get total sales
    const totalSales = await prisma.order.aggregate({
      _sum: {
        total: true,
      },
      where: {
        status: 'COMPLETED',
      },
    });

    // Get total orders
    const totalOrders = await prisma.order.count();

    // Get total products
    const totalProducts = await prisma.product.count();

    // Get total customers
    const totalCustomers = await prisma.customer.count();

    // Get low stock products
    const lowStockProducts = await prisma.product.count({
      where: {
        product_quantity: {
          lte: 10,
        },
      },
    });

    // Get recent orders
    const recentOrders = await prisma.order.findMany({
      take: 5,
      orderBy: {
        orderDate: 'desc',
      },
      include: {
        customer: true,
      },
    });

    // Get sales data for chart
    const currentDate = new Date();
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(currentDate.getMonth() - 6);

    const salesByMonth = await prisma.$queryRaw`
      SELECT 
        MONTH(orderDate) as month, 
        YEAR(orderDate) as year,
        SUM(total) as total
      FROM \`Order\`
      WHERE 
        status = 'COMPLETED' 
        AND orderDate >= ${sixMonthsAgo}
      GROUP BY MONTH(orderDate), YEAR(orderDate)
      ORDER BY YEAR(orderDate), MONTH(orderDate)
    `;

    // Format sales data for chart
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    const salesData = {
      labels: [],
      datasets: [
        {
          label: 'Sales',
          data: [],
          borderColor: 'rgb(53, 162, 235)',
          backgroundColor: 'rgba(53, 162, 235, 0.5)',
          tension: 0.3,
        },
      ],
    };

    // Fill in the sales data
    salesByMonth.forEach((item) => {
      salesData.labels.push(months[item.month - 1]);
      salesData.datasets[0].data.push(parseFloat(item.total));
    });

    // Get expense data (mock data for now)
    const expenses = [800, 1200, 1000, 1800, 1600, 2000];
    
    // Add expenses dataset
    salesData.datasets.push({
      label: 'Expenses',
      data: expenses,
      borderColor: 'rgb(255, 99, 132)',
      backgroundColor: 'rgba(255, 99, 132, 0.5)',
      tension: 0.3,
    });

    return {
      totalSales: totalSales._sum.total || 0,
      totalOrders,
      totalProducts,
      totalCustomers,
      lowStockProducts,
      recentOrders: recentOrders.map(order => ({
        id: order.id,
        orderNumber: `ORD-${order.id.toString().padStart(3, '0')}`,
        customer: `${order.customer.firstName} ${order.customer.lastName}`,
        amount: order.total,
        status: order.status,
        date: order.orderDate.toISOString().split('T')[0],
      })),
      salesData,
    };
  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    return {
      totalSales: 0,
      totalOrders: 0,
      totalProducts: 0,
      totalCustomers: 0,
      lowStockProducts: 0,
      recentOrders: [],
      salesData: {
        labels: [],
        datasets: [],
      },
    };
  }
}

// Products
export async function getProducts(filters = {}) {
  try {
    const where = {};
    
    if (filters.category) {
      where.category = {
        name: filters.category,
      };
    }
    
    if (filters.status === 'active') {
      where.is_active = true;
    } else if (filters.status === 'inactive') {
      where.is_active = false;
    }
    
    if (filters.stock === 'low') {
      where.product_quantity = {
        lte: 10,
        gt: 0,
      };
    } else if (filters.stock === 'out') {
      where.product_quantity = {
        lte: 0,
      };
    }
    
    const products = await prisma.product.findMany({
      where,
      include: {
        category: true,
        supplier: true,
      },
      orderBy: {
        id: 'asc',
      },
    });
    
    return products;
  } catch (error) {
    console.error('Error fetching products:', error);
    return [];
  }
}

// Categories
export async function getCategories() {
  try {
    const categories = await prisma.category.findMany({
      orderBy: {
        name: 'asc',
      },
    });
    
    return categories;
  } catch (error) {
    console.error('Error fetching categories:', error);
    return [];
  }
}

// Suppliers
export async function getSuppliers() {
  try {
    const suppliers = await prisma.supplier.findMany({
      orderBy: {
        name: 'asc',
      },
    });
    
    return suppliers;
  } catch (error) {
    console.error('Error fetching suppliers:', error);
    return [];
  }
}

// Customers
export async function getCustomers() {
  try {
    const customers = await prisma.customer.findMany({
      orderBy: {
        lastName: 'asc',
      },
    });
    
    return customers;
  } catch (error) {
    console.error('Error fetching customers:', error);
    return [];
  }
}

// Orders
export async function getOrders(filters = {}) {
  try {
    const where = {};
    
    if (filters.status) {
      where.status = filters.status;
    }
    
    if (filters.dateRange) {
      const currentDate = new Date();
      let startDate;
      
      switch (filters.dateRange) {
        case 'today':
          startDate = new Date(currentDate.setHours(0, 0, 0, 0));
          where.orderDate = {
            gte: startDate,
          };
          break;
        case 'week':
          startDate = new Date(currentDate);
          startDate.setDate(currentDate.getDate() - 7);
          where.orderDate = {
            gte: startDate,
          };
          break;
        case 'month':
          startDate = new Date(currentDate);
          startDate.setMonth(currentDate.getMonth() - 1);
          where.orderDate = {
            gte: startDate,
          };
          break;
        case 'year':
          startDate = new Date(currentDate);
          startDate.setFullYear(currentDate.getFullYear() - 1);
          where.orderDate = {
            gte: startDate,
          };
          break;
      }
    }
    
    const orders = await prisma.order.findMany({
      where,
      include: {
        customer: true,
        OrderItems: {
          include: {
            product: true,
          },
        },
      },
      orderBy: {
        orderDate: 'desc',
      },
    });
    
    return orders;
  } catch (error) {
    console.error('Error fetching orders:', error);
    return [];
  }
}
