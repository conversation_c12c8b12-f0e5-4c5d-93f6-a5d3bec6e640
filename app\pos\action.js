
'use server';

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// Get all products with category and subcategory information
export async function getProducts(filters = {}) {
  try {
    const where = {};

    // Apply filters
    if (filters.category && filters.category !== 'all') {
      where.categoryId = parseInt(filters.category);
    }

    if (filters.subcategory && filters.subcategory !== 'all') {
      where.subCategoryId = parseInt(filters.subcategory);
    }

    if (filters.search) {
      where.OR = [
        { name: { contains: filters.search, mode: 'insensitive' } },
        { sku: { contains: filters.search, mode: 'insensitive' } },
        { brand: { contains: filters.search, mode: 'insensitive' } }
      ];
    }

    if (filters.status === 'active') {
      where.is_active = true;
    } else if (filters.status === 'inactive') {
      where.is_active = false;
    }

    if (filters.stock === 'low') {
      where.product_quantity = {
        lte: 10,
        gt: 0,
      };
    } else if (filters.stock === 'out') {
      where.product_quantity = {
        lte: 0,
      };
    }

    const products = await prisma.product.findMany({
      where,
      include: {
        category: true,
        subCategory: true,
        supplier: true,
      },
      orderBy: {
        name: 'asc',
      },
    });

    return products;
  } catch (error) {
    console.error('Error fetching products:', error);
    return [];
  }
}

// Get all categories
export async function getCategories() {
  try {
    const categories = await prisma.category.findMany({
      include: {
        subCategories: true,
        _count: {
          select: {
            products: true,
          },
        },
      },
      orderBy: {
        sortOrder: 'asc',
      },
    });

    return categories;
  } catch (error) {
    console.error('Error fetching categories:', error);
    return [];
  }
}

// Get subcategories by category ID
export async function getSubCategories(categoryId = null) {
  try {
    const where = {};

    if (categoryId) {
      where.categoryId = parseInt(categoryId);
    }

    const subcategories = await prisma.subCategory.findMany({
      where,
      include: {
        category: true,
        _count: {
          select: {
            products: true,
          },
        },
      },
      orderBy: {
        sortOrder: 'asc',
      },
    });

    return subcategories;
  } catch (error) {
    console.error('Error fetching subcategories:', error);
    return [];
  }
}

// Get product by ID
export async function getProductById(id) {
  try {
    const product = await prisma.product.findUnique({
      where: {
        id: parseInt(id),
      },
      include: {
        category: true,
        subCategory: true,
        supplier: true,
      },
    });

    return product;
  } catch (error) {
    console.error('Error fetching product:', error);
    return null;
  }
}

// Search products for POS
export async function searchProducts(query) {
  try {
    const products = await prisma.product.findMany({
      where: {
        AND: [
          { is_active: true },
          { product_quantity: { gt: 0 } },
          {
            OR: [
              { name: { contains: query, mode: 'insensitive' } },
              { sku: { contains: query, mode: 'insensitive' } },
              { brand: { contains: query, mode: 'insensitive' } }
            ]
          }
        ]
      },
      include: {
        category: true,
        subCategory: true,
      },
      take: 20,
      orderBy: {
        name: 'asc',
      },
    });

    return products;
  } catch (error) {
    console.error('Error searching products:', error);
    return [];
  }
}

// Get products by category for POS
export async function getProductsByCategory(categoryId) {
  try {
    const products = await prisma.product.findMany({
      where: {
        categoryId: parseInt(categoryId),
        is_active: true,
        product_quantity: { gt: 0 },
      },
      include: {
        category: true,
        subCategory: true,
      },
      orderBy: {
        name: 'asc',
      },
    });

    return products;
  } catch (error) {
    console.error('Error fetching products by category:', error);
    return [];
  }
}

// Get products by subcategory for POS
export async function getProductsBySubCategory(subcategoryId) {
  try {
    const products = await prisma.product.findMany({
      where: {
        subCategoryId: parseInt(subcategoryId),
        is_active: true,
        product_quantity: { gt: 0 },
      },
      include: {
        category: true,
        subCategory: true,
      },
      orderBy: {
        name: 'asc',
      },
    });

    return products;
  } catch (error) {
    console.error('Error fetching products by subcategory:', error);
    return [];
  }
}
