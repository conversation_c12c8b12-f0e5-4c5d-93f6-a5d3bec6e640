
'use server';

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// Get all products with category and subcategory information
export async function getProducts(filters = {}) {
  try {
    const where = {};

    // Apply filters
    if (filters.category && filters.category !== 'all') {
      where.categoryId = parseInt(filters.category);
    }

    if (filters.subcategory && filters.subcategory !== 'all') {
      where.subCategoryId = parseInt(filters.subcategory);
    }

    if (filters.search) {
      where.OR = [
        { name: { contains: filters.search } },
        { sku: { contains: filters.search } },
        { brand: { contains: filters.search } }
      ];

    }

    if (filters.status === 'active') {
      where.is_active = true;
    } else if (filters.status === 'inactive') {
      where.is_active = false;
    }

    if (filters.stock === 'low') {
      where.product_quantity = {
        lte: 10,
        gt: 0,
      };
    } else if (filters.stock === 'out') {
      where.product_quantity = {
        lte: 0,
      };
    }

    // Pagination parameters
    const page = parseInt(filters.page) || 1;
    const limit = parseInt(filters.limit) || 20;
    const skip = (page - 1) * limit;

    // Get total count for pagination
    const totalCount = await prisma.product.count({ where });

    const products = await prisma.product.findMany({
      where,
      include: {
        category: true,
        subCategory: true,
        supplier: true,
      },
      orderBy: {
        name: 'asc',
      },
      skip,
      take: limit,
    });

    return {
      products,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        hasNext: page < Math.ceil(totalCount / limit),
        hasPrev: page > 1,
      }
    };
  } catch (error) {
    console.error('Error fetching products:', error);
    return {
      products: [],
      pagination: {
        page: 1,
        limit: 20,
        totalCount: 0,
        totalPages: 0,
        hasNext: false,
        hasPrev: false,
      }
    };
  }
}

// Get all categories
export async function getCategories() {
  try {
    const categories = await prisma.category.findMany({
      include: {
        subCategories: true,
        _count: {
          select: {
            products: true,
          },
        },
      },
      orderBy: {
        sortOrder: 'asc',
      },
    });

    return categories;
  } catch (error) {
    console.error('Error fetching categories:', error);
    return [];
  }
}

// Get subcategories by category ID
export async function getSubCategories(categoryId = null) {
  try {
    const where = {};

    if (categoryId) {
      where.categoryId = parseInt(categoryId);
    }

    const subcategories = await prisma.subCategory.findMany({
      where,
      include: {
        category: true,
        _count: {
          select: {
            products: true,
          },
        },
      },
      orderBy: {
        sortOrder: 'asc',
      },
    });

    return subcategories;
  } catch (error) {
    console.error('Error fetching subcategories:', error);
    return [];
  }
}

// Get product by ID
export async function getProductById(id) {
  try {
    const product = await prisma.product.findUnique({
      where: {
        id: parseInt(id),
      },
      include: {
        category: true,
        subCategory: true,
        supplier: true,
      },
    });

    return product;
  } catch (error) {
    console.error('Error fetching product:', error);
    return null;
  }
}

// Search products for POS
export async function searchProducts(query) {
  try {
    const products = await prisma.product.findMany({
      where: {
        AND: [
          { is_active: true },
          { product_quantity: { gt: 0 } },
          {
            OR: [
              { name: { contains: query } },
              { sku: { contains: query } },
              { brand: { contains: query } }
            ]
          }
        ]
      },
      include: {
        category: true,
        subCategory: true,
      },
      take: 20,
      orderBy: {
        name: 'asc',
      },
    });

    return products;
  } catch (error) {
    console.error('Error searching products:', error);
    return [];
  }
}

// Get products by category for POS
export async function getProductsByCategory(categoryId) {
  try {
    const products = await prisma.product.findMany({
      where: {
        categoryId: parseInt(categoryId),
        is_active: true,
        product_quantity: { gt: 0 },
      },
      include: {
        category: true,
        subCategory: true,
      },
      orderBy: {
        name: 'asc',
      },
    });

    return products;
  } catch (error) {
    console.error('Error fetching products by category:', error);
    return [];
  }
}

// Get products by subcategory for POS
export async function getProductsBySubCategory(subcategoryId) {
  try {
    const products = await prisma.product.findMany({
      where: {
        subCategoryId: parseInt(subcategoryId),
        is_active: true,
        product_quantity: { gt: 0 },
      },
      include: {
        category: true,
        subCategory: true,
      },
      orderBy: {
        name: 'asc',
      },
    });

    return products;
  } catch (error) {
    console.error('Error fetching products by subcategory:', error);
    return [];
  }
}

// ==================== CUSTOMER FUNCTIONS ====================

// Get all customers with pagination and search
export async function getCustomers(filters = {}) {
  try {
    const where = {};

    // Apply search filter
    if (filters.search) {
      where.OR = [
        { firstName: { contains: filters.search } },
        { lastName: { contains: filters.search } },
        { email: { contains: filters.search } },
        { phone: { contains: filters.search } },
        { cnic: { contains: filters.search } }
      ];
    }

    // Pagination parameters
    const page = parseInt(filters.page) || 1;
    const limit = parseInt(filters.limit) || 20;
    const skip = (page - 1) * limit;

    // Get total count for pagination
    const totalCount = await prisma.customer.count({ where });

    const customers = await prisma.customer.findMany({
      where,
      orderBy: {
        firstName: 'asc',
      },
      skip,
      take: limit,
    });

    return {
      customers,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        hasNext: page < Math.ceil(totalCount / limit),
        hasPrev: page > 1,
      }
    };
  } catch (error) {
    console.error('Error fetching customers:', error);
    return {
      customers: [],
      pagination: {
        page: 1,
        limit: 20,
        totalCount: 0,
        totalPages: 0,
        hasNext: false,
        hasPrev: false,
      }
    };
  }
}

// Get customer by ID
export async function getCustomerById(id) {
  try {
    const customer = await prisma.customer.findUnique({
      where: {
        id: parseInt(id),
      },
    });

    return customer;
  } catch (error) {
    console.error('Error fetching customer:', error);
    return null;
  }
}

// Search customers for POS (quick search)
export async function searchCustomers(query) {
  try {
    const customers = await prisma.customer.findMany({
      where: {
        OR: [
          { firstName: { contains: query } },
          { lastName: { contains: query } },
          { email: { contains: query } },
          { phone: { contains: query } },
          { cnic: { contains: query } }
        ]
      },
      orderBy: {
        firstName: 'asc',
      },
      take: 10, // Limit to 10 results for quick search
    });

    return customers;
  } catch (error) {
    console.error('Error searching customers:', error);
    return [];
  }
}

// Create a new customer
export async function createCustomer(customerData) {
  try {
    // Validate required fields
    if (!customerData.firstName || !customerData.email) {
      throw new Error('First name and email are required');
    }

    // Check if email already exists
    const existingCustomer = await prisma.customer.findUnique({
      where: { email: customerData.email }
    });

    if (existingCustomer) {
      throw new Error('Customer with this email already exists');
    }

    // Check if CNIC already exists (if provided)
    if (customerData.cnic) {
      const existingCnic = await prisma.customer.findUnique({
        where: { cnic: customerData.cnic }
      });

      if (existingCnic) {
        throw new Error('Customer with this CNIC already exists');
      }
    }

    const customer = await prisma.customer.create({
      data: {
        firstName: customerData.firstName.trim(),
        lastName: customerData.lastName?.trim() || null,
        email: customerData.email.trim().toLowerCase(),
        phone: customerData.phone?.trim() || null,
        cnic: customerData.cnic?.trim() || null,
      },
    });

    return { success: true, customer };
  } catch (error) {
    console.error('Error creating customer:', error);
    return {
      success: false,
      error: error.message || 'Failed to create customer'
    };
  }
}

// Update customer
export async function updateCustomer(id, customerData) {
  try {
    // Validate required fields
    if (!customerData.firstName || !customerData.email) {
      throw new Error('First name and email are required');
    }

    // Check if email already exists (excluding current customer)
    const existingCustomer = await prisma.customer.findUnique({
      where: { email: customerData.email }
    });

    if (existingCustomer && existingCustomer.id !== parseInt(id)) {
      throw new Error('Customer with this email already exists');
    }

    // Check if CNIC already exists (if provided and excluding current customer)
    if (customerData.cnic) {
      const existingCnic = await prisma.customer.findUnique({
        where: { cnic: customerData.cnic }
      });

      if (existingCnic && existingCnic.id !== parseInt(id)) {
        throw new Error('Customer with this CNIC already exists');
      }
    }

    const customer = await prisma.customer.update({
      where: { id: parseInt(id) },
      data: {
        firstName: customerData.firstName.trim(),
        lastName: customerData.lastName?.trim() || null,
        email: customerData.email.trim().toLowerCase(),
        phone: customerData.phone?.trim() || null,
        cnic: customerData.cnic?.trim() || null,
      },
    });

    return { success: true, customer };
  } catch (error) {
    console.error('Error updating customer:', error);
    return {
      success: false,
      error: error.message || 'Failed to update customer'
    };
  }
}

// Delete customer
export async function deleteCustomer(id) {
  try {
    await prisma.customer.delete({
      where: { id: parseInt(id) },
    });

    return { success: true };
  } catch (error) {
    console.error('Error deleting customer:', error);
    return {
      success: false,
      error: error.message || 'Failed to delete customer'
    };
  }
}

// Get customer by email
export async function getCustomerByEmail(email) {
  try {
    const customer = await prisma.customer.findUnique({
      where: {
        email: email.toLowerCase(),
      },
    });

    return customer;
  } catch (error) {
    console.error('Error fetching customer by email:', error);
    return null;
  }
}

// Get customer by phone
export async function getCustomerByPhone(phone) {
  try {
    const customer = await prisma.customer.findFirst({
      where: {
        phone: phone,
      },
    });

    return customer;
  } catch (error) {
    console.error('Error fetching customer by phone:', error);
    return null;
  }
}

// Get customer by CNIC
export async function getCustomerByCnic(cnic) {
  try {
    const customer = await prisma.customer.findUnique({
      where: {
        cnic: cnic,
      },
    });

    return customer;
  } catch (error) {
    console.error('Error fetching customer by CNIC:', error);
    return null;
  }
}

// Get recent customers (last 10)
export async function getRecentCustomers() {
  try {
    const customers = await prisma.customer.findMany({
      orderBy: {
        createdAt: 'desc',
      },
      take: 10,
    });

    return customers;
  } catch (error) {
    console.error('Error fetching recent customers:', error);
    return [];
  }
}

// ==================== ORDER FUNCTIONS ====================

// Generate unique order number
function generateOrderNumber() {
  const timestamp = Date.now().toString();
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  return `ORD-${timestamp.slice(-6)}-${random}`;
}

// Create a new order
export async function createOrder(orderData) {
  try {
    const { customerId, items, totalAmount, taxAmount, discountAmount, paymentMethod, amountReceived } = orderData;

    // Validate required fields
    if (!items || items.length === 0) {
      throw new Error('Order must contain at least one item');
    }

    if (!totalAmount || totalAmount <= 0) {
      throw new Error('Total amount must be greater than 0');
    }

    // Generate order number
    const orderNumber = generateOrderNumber();

    // Start transaction
    const result = await prisma.$transaction(async (tx) => {
      // Create the order
      const order = await tx.order.create({
        data: {
          orderNumber,
          customerId: customerId || null,
          status: 'PENDING',
          totalAmount: parseFloat(totalAmount),
          taxAmount: parseFloat(taxAmount) || 0,
          discountAmount: parseFloat(discountAmount) || 0,
        },
      });

      // Create order items and update inventory
      const orderItems = [];
      for (const item of items) {
        // Check product availability
        const product = await tx.product.findUnique({
          where: { id: parseInt(item.productId) }
        });

        if (!product) {
          throw new Error(`Product with ID ${item.productId} not found`);
        }

        if (product.product_quantity < item.quantity) {
          throw new Error(`Insufficient stock for ${product.name}. Available: ${product.product_quantity}, Required: ${item.quantity}`);
        }

        // Create order item
        const orderItem = await tx.orderitem.create({
          data: {
            orderId: order.id,
            productId: parseInt(item.productId),
            quantity: parseInt(item.quantity),
            unitPrice: parseFloat(item.unitPrice),
            discount: parseFloat(item.discount) || 0,
            totalPrice: parseFloat(item.totalPrice),
          },
        });

        orderItems.push(orderItem);

        // Update product inventory
        await tx.product.update({
          where: { id: parseInt(item.productId) },
          data: {
            product_quantity: {
              decrement: parseInt(item.quantity)
            }
          }
        });
      }

      // Create payment record
      const payment = await tx.payment.create({
        data: {
          orderId: order.id,
          method: paymentMethod || 'CASH',
          status: 'COMPLETED',
          amount: parseFloat(amountReceived) || parseFloat(totalAmount),
          paidAt: new Date(),
        },
      });

      // Update order status to completed
      const completedOrder = await tx.order.update({
        where: { id: order.id },
        data: { status: 'COMPLETED' },
        include: {
          orderitem: {
            include: {
              product: true
            }
          },
          payment: true,
          customer: true
        }
      });

      return { order: completedOrder, payment, orderItems };
    });

    return { success: true, ...result };
  } catch (error) {
    console.error('Error creating order:', error);
    return {
      success: false,
      error: error.message || 'Failed to create order'
    };
  }
}

// Get all orders with pagination and filters
export async function getOrders(filters = {}) {
  try {
    const where = {};

    // Apply filters
    if (filters.status && filters.status !== 'all') {
      where.status = filters.status;
    }

    if (filters.customerId) {
      where.customerId = parseInt(filters.customerId);
    }

    if (filters.dateFrom) {
      where.createdAt = {
        ...where.createdAt,
        gte: new Date(filters.dateFrom)
      };
    }

    if (filters.dateTo) {
      where.createdAt = {
        ...where.createdAt,
        lte: new Date(filters.dateTo)
      };
    }

    if (filters.search) {
      where.OR = [
        { orderNumber: { contains: filters.search } },
        { customer: { firstName: { contains: filters.search } } },
        { customer: { lastName: { contains: filters.search } } },
        { customer: { email: { contains: filters.search } } }
      ];
    }

    // Pagination parameters
    const page = parseInt(filters.page) || 1;
    const limit = parseInt(filters.limit) || 20;
    const skip = (page - 1) * limit;

    // Get total count for pagination
    const totalCount = await prisma.order.count({ where });

    const orders = await prisma.order.findMany({
      where,
      include: {
        customer: true,
        orderitem: {
          include: {
            product: true
          }
        },
        payment: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
      skip,
      take: limit,
    });

    return {
      orders,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        hasNext: page < Math.ceil(totalCount / limit),
        hasPrev: page > 1,
      }
    };
  } catch (error) {
    console.error('Error fetching orders:', error);
    return {
      orders: [],
      pagination: {
        page: 1,
        limit: 20,
        totalCount: 0,
        totalPages: 0,
        hasNext: false,
        hasPrev: false,
      }
    };
  }
}

// Get order by ID
export async function getOrderById(id) {
  try {
    const order = await prisma.order.findUnique({
      where: { id: parseInt(id) },
      include: {
        customer: true,
        orderitem: {
          include: {
            product: {
              include: {
                category: true,
                subCategory: true
              }
            }
          }
        },
        payment: true,
      },
    });

    return order;
  } catch (error) {
    console.error('Error fetching order:', error);
    return null;
  }
}

// Get order by order number
export async function getOrderByNumber(orderNumber) {
  try {
    const order = await prisma.order.findUnique({
      where: { orderNumber },
      include: {
        customer: true,
        orderitem: {
          include: {
            product: {
              include: {
                category: true,
                subCategory: true
              }
            }
          }
        },
        payment: true,
      },
    });

    return order;
  } catch (error) {
    console.error('Error fetching order by number:', error);
    return null;
  }
}

// Update order status
export async function updateOrderStatus(id, status) {
  try {
    const validStatuses = ['PENDING', 'PROCESSING', 'COMPLETED', 'CANCELLED', 'REFUNDED'];

    if (!validStatuses.includes(status)) {
      throw new Error('Invalid order status');
    }

    const order = await prisma.order.update({
      where: { id: parseInt(id) },
      data: { status },
      include: {
        customer: true,
        orderitem: {
          include: {
            product: true
          }
        },
        payment: true,
      },
    });

    return { success: true, order };
  } catch (error) {
    console.error('Error updating order status:', error);
    return {
      success: false,
      error: error.message || 'Failed to update order status'
    };
  }
}

// Cancel order and restore inventory
export async function cancelOrder(id, reason = '') {
  try {
    const result = await prisma.$transaction(async (tx) => {
      // Get order with items
      const order = await tx.order.findUnique({
        where: { id: parseInt(id) },
        include: {
          orderitem: true,
          payment: true
        }
      });

      if (!order) {
        throw new Error('Order not found');
      }

      if (order.status === 'CANCELLED' || order.status === 'REFUNDED') {
        throw new Error('Order is already cancelled or refunded');
      }

      // Restore inventory for each item
      for (const item of order.orderitem) {
        await tx.product.update({
          where: { id: item.productId },
          data: {
            product_quantity: {
              increment: item.quantity
            }
          }
        });
      }

      // Update order status
      const cancelledOrder = await tx.order.update({
        where: { id: parseInt(id) },
        data: { status: 'CANCELLED' },
        include: {
          customer: true,
          orderitem: {
            include: {
              product: true
            }
          },
          payment: true,
        }
      });

      // Update payment status if exists
      if (order.payment) {
        await tx.payment.update({
          where: { id: order.payment.id },
          data: { status: 'REFUNDED' }
        });
      }

      return cancelledOrder;
    });

    return { success: true, order: result };
  } catch (error) {
    console.error('Error cancelling order:', error);
    return {
      success: false,
      error: error.message || 'Failed to cancel order'
    };
  }
}

// ==================== PAYMENT FUNCTIONS ====================

// Get payment by order ID
export async function getPaymentByOrderId(orderId) {
  try {
    const payment = await prisma.payment.findUnique({
      where: { orderId: parseInt(orderId) },
      include: {
        order: {
          include: {
            customer: true,
            orderitem: {
              include: {
                product: true
              }
            }
          }
        }
      }
    });

    return payment;
  } catch (error) {
    console.error('Error fetching payment:', error);
    return null;
  }
}

// Update payment status
export async function updatePaymentStatus(paymentId, status) {
  try {
    const validStatuses = ['PENDING', 'COMPLETED', 'FAILED', 'REFUNDED'];

    if (!validStatuses.includes(status)) {
      throw new Error('Invalid payment status');
    }

    const payment = await prisma.payment.update({
      where: { id: parseInt(paymentId) },
      data: {
        status,
        paidAt: status === 'COMPLETED' ? new Date() : null
      },
      include: {
        order: {
          include: {
            customer: true
          }
        }
      }
    });

    return { success: true, payment };
  } catch (error) {
    console.error('Error updating payment status:', error);
    return {
      success: false,
      error: error.message || 'Failed to update payment status'
    };
  }
}

// ==================== INVENTORY FUNCTIONS ====================

// Update product inventory
export async function updateProductInventory(productId, quantity, operation = 'set') {
  try {
    const product = await prisma.product.findUnique({
      where: { id: parseInt(productId) }
    });

    if (!product) {
      throw new Error('Product not found');
    }

    let updateData = {};

    if (operation === 'increment') {
      updateData = { product_quantity: { increment: parseInt(quantity) } };
    } else if (operation === 'decrement') {
      updateData = { product_quantity: { decrement: parseInt(quantity) } };
    } else {
      updateData = { product_quantity: parseInt(quantity) };
    }

    const updatedProduct = await prisma.product.update({
      where: { id: parseInt(productId) },
      data: updateData,
      include: {
        category: true,
        subCategory: true
      }
    });

    return { success: true, product: updatedProduct };
  } catch (error) {
    console.error('Error updating inventory:', error);
    return {
      success: false,
      error: error.message || 'Failed to update inventory'
    };
  }
}

// Get low stock products
export async function getLowStockProducts(threshold = 10) {
  try {
    const products = await prisma.product.findMany({
      where: {
        product_quantity: {
          lte: threshold,
          gt: 0
        },
        is_active: true
      },
      include: {
        category: true,
        subCategory: true
      },
      orderBy: {
        product_quantity: 'asc'
      }
    });

    return products;
  } catch (error) {
    console.error('Error fetching low stock products:', error);
    return [];
  }
}

// Get out of stock products
export async function getOutOfStockProducts() {
  try {
    const products = await prisma.product.findMany({
      where: {
        product_quantity: {
          lte: 0
        },
        is_active: true
      },
      include: {
        category: true,
        subCategory: true
      },
      orderBy: {
        name: 'asc'
      }
    });

    return products;
  } catch (error) {
    console.error('Error fetching out of stock products:', error);
    return [];
  }
}

// ==================== ANALYTICS & REPORTS ====================

// Get sales summary for a date range
export async function getSalesSummary(dateFrom, dateTo) {
  try {
    const where = {
      status: 'COMPLETED',
      createdAt: {
        gte: new Date(dateFrom),
        lte: new Date(dateTo)
      }
    };

    // Get total sales
    const totalSales = await prisma.order.aggregate({
      where,
      _sum: {
        totalAmount: true,
        taxAmount: true,
        discountAmount: true
      },
      _count: {
        id: true
      }
    });

    // Get sales by payment method
    const paymentMethods = await prisma.payment.groupBy({
      by: ['method'],
      where: {
        status: 'COMPLETED',
        order: {
          status: 'COMPLETED',
          createdAt: {
            gte: new Date(dateFrom),
            lte: new Date(dateTo)
          }
        }
      },
      _sum: {
        amount: true
      },
      _count: {
        id: true
      }
    });

    // Get top selling products
    const topProducts = await prisma.orderItem.groupBy({
      by: ['productId'],
      where: {
        order: {
          status: 'COMPLETED',
          createdAt: {
            gte: new Date(dateFrom),
            lte: new Date(dateTo)
          }
        }
      },
      _sum: {
        quantity: true,
        totalPrice: true
      },
      orderBy: {
        _sum: {
          quantity: 'desc'
        }
      },
      take: 10
    });

    // Get product details for top selling products
    const topProductsWithDetails = await Promise.all(
      topProducts.map(async (item) => {
        const product = await prisma.product.findUnique({
          where: { id: item.productId },
          include: {
            category: true,
            subCategory: true
          }
        });
        return {
          ...item,
          product
        };
      })
    );

    return {
      totalSales: totalSales._sum.totalAmount || 0,
      totalTax: totalSales._sum.taxAmount || 0,
      totalDiscount: totalSales._sum.discountAmount || 0,
      totalOrders: totalSales._count.id || 0,
      paymentMethods,
      topProducts: topProductsWithDetails
    };
  } catch (error) {
    console.error('Error fetching sales summary:', error);
    return {
      totalSales: 0,
      totalTax: 0,
      totalDiscount: 0,
      totalOrders: 0,
      paymentMethods: [],
      topProducts: []
    };
  }
}

// Get daily sales for a date range
export async function getDailySales(dateFrom, dateTo) {
  try {
    const dailySales = await prisma.$queryRaw`
      SELECT
        DATE(createdAt) as date,
        COUNT(*) as orderCount,
        SUM(totalAmount) as totalSales,
        SUM(taxAmount) as totalTax,
        SUM(discountAmount) as totalDiscount
      FROM \`order\`
      WHERE status = 'COMPLETED'
        AND createdAt >= ${new Date(dateFrom)}
        AND createdAt <= ${new Date(dateTo)}
      GROUP BY DATE(createdAt)
      ORDER BY date ASC
    `;

    return dailySales;
  } catch (error) {
    console.error('Error fetching daily sales:', error);
    return [];
  }
}

// Get customer analytics
export async function getCustomerAnalytics(dateFrom, dateTo) {
  try {
    // Top customers by total spent
    const topCustomers = await prisma.order.groupBy({
      by: ['customerId'],
      where: {
        status: 'COMPLETED',
        customerId: { not: null },
        createdAt: {
          gte: new Date(dateFrom),
          lte: new Date(dateTo)
        }
      },
      _sum: {
        totalAmount: true
      },
      _count: {
        id: true
      },
      orderBy: {
        _sum: {
          totalAmount: 'desc'
        }
      },
      take: 10
    });

    // Get customer details
    const topCustomersWithDetails = await Promise.all(
      topCustomers.map(async (item) => {
        const customer = await prisma.customer.findUnique({
          where: { id: item.customerId }
        });
        return {
          ...item,
          customer
        };
      })
    );

    // New customers count
    const newCustomers = await prisma.customer.count({
      where: {
        createdAt: {
          gte: new Date(dateFrom),
          lte: new Date(dateTo)
        }
      }
    });

    return {
      topCustomers: topCustomersWithDetails,
      newCustomers
    };
  } catch (error) {
    console.error('Error fetching customer analytics:', error);
    return {
      topCustomers: [],
      newCustomers: 0
    };
  }
}

// ==================== DASHBOARD & UTILITY FUNCTIONS ====================

// Get dashboard statistics
export async function getDashboardStats() {
  try {
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    const startOfYear = new Date(today.getFullYear(), 0, 1);

    // Today's sales
    const todaySales = await prisma.order.aggregate({
      where: {
        status: 'COMPLETED',
        createdAt: { gte: startOfDay }
      },
      _sum: { totalAmount: true },
      _count: { id: true }
    });

    // This month's sales
    const monthSales = await prisma.order.aggregate({
      where: {
        status: 'COMPLETED',
        createdAt: { gte: startOfMonth }
      },
      _sum: { totalAmount: true },
      _count: { id: true }
    });

    // This year's sales
    const yearSales = await prisma.order.aggregate({
      where: {
        status: 'COMPLETED',
        createdAt: { gte: startOfYear }
      },
      _sum: { totalAmount: true },
      _count: { id: true }
    });

    // Total customers
    const totalCustomers = await prisma.customer.count();

    // Total products
    const totalProducts = await prisma.product.count({
      where: { is_active: true }
    });

    // Low stock count
    const lowStockCount = await prisma.product.count({
      where: {
        product_quantity: { lte: 10, gt: 0 },
        is_active: true
      }
    });

    // Out of stock count
    const outOfStockCount = await prisma.product.count({
      where: {
        product_quantity: { lte: 0 },
        is_active: true
      }
    });

    // Recent orders
    const recentOrders = await prisma.order.findMany({
      take: 5,
      orderBy: { createdAt: 'desc' },
      include: {
        customer: true,
        payment: true
      }
    });

    return {
      todaySales: {
        amount: todaySales._sum.totalAmount || 0,
        count: todaySales._count.id || 0
      },
      monthSales: {
        amount: monthSales._sum.totalAmount || 0,
        count: monthSales._count.id || 0
      },
      yearSales: {
        amount: yearSales._sum.totalAmount || 0,
        count: yearSales._count.id || 0
      },
      totalCustomers,
      totalProducts,
      lowStockCount,
      outOfStockCount,
      recentOrders
    };
  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    return {
      todaySales: { amount: 0, count: 0 },
      monthSales: { amount: 0, count: 0 },
      yearSales: { amount: 0, count: 0 },
      totalCustomers: 0,
      totalProducts: 0,
      lowStockCount: 0,
      outOfStockCount: 0,
      recentOrders: []
    };
  }
}

// Get recent activities (orders, customers, etc.)
export async function getRecentActivities(limit = 20) {
  try {
    // Recent orders
    const recentOrders = await prisma.order.findMany({
      take: limit,
      orderBy: { createdAt: 'desc' },
      include: {
        customer: true,
        payment: true
      }
    });

    // Recent customers
    const recentCustomers = await prisma.customer.findMany({
      take: 10,
      orderBy: { createdAt: 'desc' }
    });

    return {
      recentOrders,
      recentCustomers
    };
  } catch (error) {
    console.error('Error fetching recent activities:', error);
    return {
      recentOrders: [],
      recentCustomers: []
    };
  }
}

// Search across all entities (products, customers, orders)
export async function globalSearch(query, limit = 10) {
  try {
    const searchResults = await Promise.all([
      // Search products
      prisma.product.findMany({
        where: {
          OR: [
            { name: { contains: query } },
            { sku: { contains: query } },
            { brand: { contains: query } }
          ],
          is_active: true
        },
        include: {
          category: true,
          subCategory: true
        },
        take: limit
      }),

      // Search customers
      prisma.customer.findMany({
        where: {
          OR: [
            { firstName: { contains: query } },
            { lastName: { contains: query } },
            { email: { contains: query } },
            { phone: { contains: query } }
          ]
        },
        take: limit
      }),

      // Search orders
      prisma.order.findMany({
        where: {
          OR: [
            { orderNumber: { contains: query } },
            { customer: { firstName: { contains: query } } },
            { customer: { lastName: { contains: query } } }
          ]
        },
        include: {
          customer: true,
          payment: true
        },
        take: limit
      })
    ]);

    return {
      products: searchResults[0],
      customers: searchResults[1],
      orders: searchResults[2]
    };
  } catch (error) {
    console.error('Error performing global search:', error);
    return {
      products: [],
      customers: [],
      orders: []
    };
  }
}

// Validate product availability for cart
export async function validateCartItems(cartItems) {
  try {
    const validationResults = [];

    for (const item of cartItems) {
      const product = await prisma.product.findUnique({
        where: { id: parseInt(item.productId) },
        include: {
          category: true,
          subCategory: true
        }
      });

      if (!product) {
        validationResults.push({
          productId: item.productId,
          valid: false,
          error: 'Product not found',
          product: null
        });
        continue;
      }

      if (!product.is_active) {
        validationResults.push({
          productId: item.productId,
          valid: false,
          error: 'Product is inactive',
          product
        });
        continue;
      }

      if (product.product_quantity < item.quantity) {
        validationResults.push({
          productId: item.productId,
          valid: false,
          error: `Insufficient stock. Available: ${product.product_quantity}, Required: ${item.quantity}`,
          product,
          availableQuantity: product.product_quantity
        });
        continue;
      }

      validationResults.push({
        productId: item.productId,
        valid: true,
        error: null,
        product,
        availableQuantity: product.product_quantity
      });
    }

    return {
      valid: validationResults.every(result => result.valid),
      results: validationResults
    };
  } catch (error) {
    console.error('Error validating cart items:', error);
    return {
      valid: false,
      results: [],
      error: error.message
    };
  }
}

// Get system health status
export async function getSystemHealth() {
  try {
    // Check database connection
    await prisma.$queryRaw`SELECT 1`;

    // Get basic counts
    const [productCount, customerCount, orderCount] = await Promise.all([
      prisma.product.count(),
      prisma.customer.count(),
      prisma.order.count()
    ]);

    return {
      status: 'healthy',
      database: 'connected',
      productCount,
      customerCount,
      orderCount,
      timestamp: new Date()
    };
  } catch (error) {
    console.error('System health check failed:', error);
    return {
      status: 'unhealthy',
      database: 'disconnected',
      error: error.message,
      timestamp: new Date()
    };
  }
}

// ==================== LOAN MANAGEMENT FUNCTIONS ====================

// Generate unique loan number
function generateLoanNumber() {
  const timestamp = Date.now().toString();
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  return `LOAN-${timestamp.slice(-6)}-${random}`;
}

// Create a new loan
export async function createLoan(loanData) {
  try {
    const { customerId, items, totalAmount, interestRate, duration, notes } = loanData;

    // Validate required fields
    if (!customerId || !items || items.length === 0) {
      throw new Error('Customer and items are required for loan');
    }

    // Generate loan number
    const loanNumber = generateLoanNumber();

    // Calculate due date
    const startDate = new Date();
    const dueDate = new Date(startDate.getTime() + (duration * 24 * 60 * 60 * 1000));

    // Start transaction
    const result = await prisma.$transaction(async (tx) => {
      // Create the loan
      const loan = await tx.loan.create({
        data: {
          loanNumber,
          customerId: parseInt(customerId),
          totalAmount: parseFloat(totalAmount),
          interestRate: parseFloat(interestRate) || 0,
          duration: parseInt(duration),
          startDate,
          dueDate,
          remainingAmount: parseFloat(totalAmount),
          notes: notes || null,
        },
      });

      // Create loan items and update inventory
      const loanItems = [];
      for (const item of items) {
        // Check product availability
        const product = await tx.product.findUnique({
          where: { id: parseInt(item.productId) }
        });

        if (!product) {
          throw new Error(`Product with ID ${item.productId} not found`);
        }

        if (product.product_quantity < item.quantity) {
          throw new Error(`Insufficient stock for ${product.name}. Available: ${product.product_quantity}, Required: ${item.quantity}`);
        }

        // Create loan item
        const loanItem = await tx.loanitem.create({
          data: {
            loanId: loan.id,
            productId: parseInt(item.productId),
            quantity: parseInt(item.quantity),
            unitPrice: parseFloat(item.unitPrice),
            totalPrice: parseFloat(item.totalPrice),
          },
        });

        loanItems.push(loanItem);

        // Update product inventory
        await tx.product.update({
          where: { id: parseInt(item.productId) },
          data: {
            product_quantity: {
              decrement: parseInt(item.quantity)
            }
          }
        });
      }

      // Update customer balance
      await tx.customer.update({
        where: { id: parseInt(customerId) },
        data: {
          currentBalance: {
            increment: parseFloat(totalAmount)
          }
        }
      });

      return { loan, loanItems };
    });

    return { success: true, ...result };
  } catch (error) {
    console.error('Error creating loan:', error);
    return {
      success: false,
      error: error.message || 'Failed to create loan'
    };
  }
}

// Get all loans with pagination and filters
export async function getLoans(filters = {}) {
  try {
    const where = {};

    // Apply filters
    if (filters.status && filters.status !== 'all') {
      where.status = filters.status;
    }

    if (filters.customerId) {
      where.customerId = parseInt(filters.customerId);
    }

    if (filters.search) {
      where.OR = [
        { loanNumber: { contains: filters.search } },
        { customer: { firstName: { contains: filters.search } } },
        { customer: { lastName: { contains: filters.search } } }
      ];
    }

    // Pagination parameters
    const page = parseInt(filters.page) || 1;
    const limit = parseInt(filters.limit) || 20;
    const skip = (page - 1) * limit;

    // Get total count for pagination
    const totalCount = await prisma.loan.count({ where });

    const loans = await prisma.loan.findMany({
      where,
      include: {
        customer: true,
        loanitem: {
          include: {
            product: true
          }
        },
        loanpayment: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
      skip,
      take: limit,
    });

    return {
      loans,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        hasNext: page < Math.ceil(totalCount / limit),
        hasPrev: page > 1,
      }
    };
  } catch (error) {
    console.error('Error fetching loans:', error);
    return {
      loans: [],
      pagination: {
        page: 1,
        limit: 20,
        totalCount: 0,
        totalPages: 0,
        hasNext: false,
        hasPrev: false,
      }
    };
  }
}

// Make loan payment
export async function makeLoanPayment(loanId, paymentData) {
  try {
    const { amount, method, notes } = paymentData;

    const result = await prisma.$transaction(async (tx) => {
      // Get loan details
      const loan = await tx.loan.findUnique({
        where: { id: parseInt(loanId) }
      });

      if (!loan) {
        throw new Error('Loan not found');
      }

      if (loan.status === 'COMPLETED' || loan.status === 'CANCELLED') {
        throw new Error('Cannot make payment on completed or cancelled loan');
      }

      const paymentAmount = parseFloat(amount);
      if (paymentAmount > loan.remainingAmount) {
        throw new Error('Payment amount cannot exceed remaining amount');
      }

      // Create loan payment
      const loanPayment = await tx.loanpayment.create({
        data: {
          loanId: parseInt(loanId),
          amount: paymentAmount,
          method: method || 'CASH',
          notes: notes || null,
        },
      });

      // Update loan
      const newPaidAmount = loan.paidAmount + paymentAmount;
      const newRemainingAmount = loan.remainingAmount - paymentAmount;
      const newStatus = newRemainingAmount <= 0 ? 'COMPLETED' : loan.status;

      const updatedLoan = await tx.loan.update({
        where: { id: parseInt(loanId) },
        data: {
          paidAmount: newPaidAmount,
          remainingAmount: newRemainingAmount,
          status: newStatus,
        },
        include: {
          customer: true,
          loanitem: {
            include: {
              product: true
            }
          },
          loanpayment: true,
        }
      });

      // Update customer balance
      await tx.customer.update({
        where: { id: loan.customerId },
        data: {
          currentBalance: {
            decrement: paymentAmount
          }
        }
      });

      return { loan: updatedLoan, payment: loanPayment };
    });

    return { success: true, ...result };
  } catch (error) {
    console.error('Error making loan payment:', error);
    return {
      success: false,
      error: error.message || 'Failed to make loan payment'
    };
  }
}

// ==================== RENTAL MANAGEMENT FUNCTIONS ====================

// Generate unique rental number
function generateRentalNumber() {
  const timestamp = Date.now().toString();
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  return `RENT-${timestamp.slice(-6)}-${random}`;
}

// Create a new rental
export async function createRental(rentalData) {
  try {
    const { customerId, items, dailyRate, securityDeposit, notes } = rentalData;

    // Validate required fields
    if (!customerId || !items || items.length === 0) {
      throw new Error('Customer and items are required for rental');
    }

    // Generate rental number
    const rentalNumber = generateRentalNumber();

    // Calculate total amount
    const totalAmount = items.reduce((sum, item) => sum + parseFloat(item.totalPrice), 0);

    // Start transaction
    const result = await prisma.$transaction(async (tx) => {
      // Create the rental
      const rental = await tx.rental.create({
        data: {
          rentalNumber,
          customerId: parseInt(customerId),
          totalAmount,
          dailyRate: parseFloat(dailyRate) || 0,
          securityDeposit: parseFloat(securityDeposit) || 0,
          notes: notes || null,
        },
      });

      // Create rental items and update inventory
      const rentalItems = [];
      for (const item of items) {
        // Check product availability
        const product = await tx.product.findUnique({
          where: { id: parseInt(item.productId) }
        });

        if (!product) {
          throw new Error(`Product with ID ${item.productId} not found`);
        }

        if (product.product_quantity < item.quantity) {
          throw new Error(`Insufficient stock for ${product.name}. Available: ${product.product_quantity}, Required: ${item.quantity}`);
        }

        // Create rental item
        const rentalItem = await tx.rentalitem.create({
          data: {
            rentalId: rental.id,
            productId: parseInt(item.productId),
            quantity: parseInt(item.quantity),
            dailyRate: parseFloat(item.dailyRate),
            totalDays: parseInt(item.totalDays),
            totalPrice: parseFloat(item.totalPrice),
          },
        });

        rentalItems.push(rentalItem);

        // Update product inventory (mark as rented)
        await tx.product.update({
          where: { id: parseInt(item.productId) },
          data: {
            product_quantity: {
              decrement: parseInt(item.quantity)
            }
          }
        });
      }

      return { rental, rentalItems };
    });

    return { success: true, ...result };
  } catch (error) {
    console.error('Error creating rental:', error);
    return {
      success: false,
      error: error.message || 'Failed to create rental'
    };
  }
}

// Get all rentals with pagination and filters
export async function getRentals(filters = {}) {
  try {
    const where = {};

    // Apply filters
    if (filters.status && filters.status !== 'all') {
      where.status = filters.status;
    }

    if (filters.customerId) {
      where.customerId = parseInt(filters.customerId);
    }

    if (filters.search) {
      where.OR = [
        { rentalNumber: { contains: filters.search } },
        { customer: { firstName: { contains: filters.search } } },
        { customer: { lastName: { contains: filters.search } } }
      ];
    }

    // Pagination parameters
    const page = parseInt(filters.page) || 1;
    const limit = parseInt(filters.limit) || 20;
    const skip = (page - 1) * limit;

    // Get total count for pagination
    const totalCount = await prisma.rental.count({ where });

    const rentals = await prisma.rental.findMany({
      where,
      include: {
        customer: true,
        rentalitem: {
          include: {
            product: true
          }
        },
        rentalpayment: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
      skip,
      take: limit,
    });

    return {
      rentals,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        hasNext: page < Math.ceil(totalCount / limit),
        hasPrev: page > 1,
      }
    };
  } catch (error) {
    console.error('Error fetching rentals:', error);
    return {
      rentals: [],
      pagination: {
        page: 1,
        limit: 20,
        totalCount: 0,
        totalPages: 0,
        hasNext: false,
        hasPrev: false,
      }
    };
  }
}

// Return rental items
export async function returnRental(rentalId, returnData) {
  try {
    const { returnDate, notes } = returnData;

    const result = await prisma.$transaction(async (tx) => {
      // Get rental details
      const rental = await tx.rental.findUnique({
        where: { id: parseInt(rentalId) },
        include: {
          rentalitem: {
            include: {
              product: true
            }
          }
        }
      });

      if (!rental) {
        throw new Error('Rental not found');
      }

      if (rental.status === 'RETURNED' || rental.status === 'CANCELLED') {
        throw new Error('Rental is already returned or cancelled');
      }

      // Update rental status
      const updatedRental = await tx.rental.update({
        where: { id: parseInt(rentalId) },
        data: {
          status: 'RETURNED',
          returnDate: returnDate ? new Date(returnDate) : new Date(),
          notes: notes || rental.notes,
        },
        include: {
          customer: true,
          rentalitem: {
            include: {
              product: true
            }
          },
          rentalpayment: true,
        }
      });

      // Restore inventory for each item
      for (const item of rental.rentalitem) {
        await tx.product.update({
          where: { id: item.productId },
          data: {
            product_quantity: {
              increment: item.quantity
            }
          }
        });
      }

      return updatedRental;
    });

    return { success: true, rental: result };
  } catch (error) {
    console.error('Error returning rental:', error);
    return {
      success: false,
      error: error.message || 'Failed to return rental'
    };
  }
}

// Make rental payment
export async function makeRentalPayment(rentalId, paymentData) {
  try {
    const { amount, method, notes } = paymentData;

    const result = await prisma.$transaction(async (tx) => {
      // Get rental details
      const rental = await tx.rental.findUnique({
        where: { id: parseInt(rentalId) }
      });

      if (!rental) {
        throw new Error('Rental not found');
      }

      if (rental.status === 'CANCELLED') {
        throw new Error('Cannot make payment on cancelled rental');
      }

      const paymentAmount = parseFloat(amount);

      // Create rental payment
      const rentalPayment = await tx.rentalpayment.create({
        data: {
          rentalId: parseInt(rentalId),
          amount: paymentAmount,
          method: method || 'CASH',
          notes: notes || null,
        },
      });

      // Update rental paid amount
      const updatedRental = await tx.rental.update({
        where: { id: parseInt(rentalId) },
        data: {
          paidAmount: {
            increment: paymentAmount
          }
        },
        include: {
          customer: true,
          rentalitem: {
            include: {
              product: true
            }
          },
          rentalpayment: true,
        }
      });

      return { rental: updatedRental, payment: rentalPayment };
    });

    return { success: true, ...result };
  } catch (error) {
    console.error('Error making rental payment:', error);
    return {
      success: false,
      error: error.message || 'Failed to make rental payment'
    };
  }
}

// ==================== INVENTORY MANAGEMENT FUNCTIONS ====================

// Get all products with advanced filtering and pagination for inventory
export async function getInventoryProducts(filters = {}) {
  try {
    const where = {};

    // Apply filters
    if (filters.category && filters.category !== 'all') {
      where.categoryId = parseInt(filters.category);
    }

    if (filters.subcategory && filters.subcategory !== 'all') {
      where.subCategoryId = parseInt(filters.subcategory);
    }

    if (filters.supplier && filters.supplier !== 'all') {
      where.supplierId = parseInt(filters.supplier);
    }

    if (filters.status && filters.status !== 'all') {
      if (filters.status === 'active') {
        where.is_active = true;
      } else if (filters.status === 'inactive') {
        where.is_active = false;
      } else if (filters.status === 'low_stock') {
        where.product_quantity = { lte: parseInt(filters.lowStockThreshold) || 10 };
        where.is_active = true;
      } else if (filters.status === 'out_of_stock') {
        where.product_quantity = { lte: 0 };
        where.is_active = true;
      }
    }

    if (filters.search) {
      where.OR = [
        { name: { contains: filters.search } },
        { sku: { contains: filters.search } },
        { brand: { contains: filters.search } },
        { description: { contains: filters.search } }
      ];
    }

    if (filters.priceMin) {
      where.price = { ...where.price, gte: parseFloat(filters.priceMin) };
    }

    if (filters.priceMax) {
      where.price = { ...where.price, lte: parseFloat(filters.priceMax) };
    }

    // Pagination parameters
    const page = parseInt(filters.page) || 1;
    const limit = parseInt(filters.limit) || 50;
    const skip = (page - 1) * limit;

    // Sorting
    let orderBy = { name: 'asc' };
    if (filters.sortBy) {
      switch (filters.sortBy) {
        case 'name_asc':
          orderBy = { name: 'asc' };
          break;
        case 'name_desc':
          orderBy = { name: 'desc' };
          break;
        case 'price_asc':
          orderBy = { price: 'asc' };
          break;
        case 'price_desc':
          orderBy = { price: 'desc' };
          break;
        case 'stock_asc':
          orderBy = { product_quantity: 'asc' };
          break;
        case 'stock_desc':
          orderBy = { product_quantity: 'desc' };
          break;
        case 'created_asc':
          orderBy = { createdAt: 'asc' };
          break;
        case 'created_desc':
          orderBy = { createdAt: 'desc' };
          break;
        default:
          orderBy = { name: 'asc' };
      }
    }

    // Get total count for pagination
    const totalCount = await prisma.product.count({ where });

    const products = await prisma.product.findMany({
      where,
      include: {
        category: true,
        subCategory: true,
        supplier: true,
      },
      orderBy,
      skip,
      take: limit,
    });

    // Calculate inventory statistics
    const stats = await getInventoryStats(where);

    return {
      products,
      stats,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        hasNext: page < Math.ceil(totalCount / limit),
        hasPrev: page > 1,
      }
    };
  } catch (error) {
    console.error('Error fetching inventory products:', error);
    return {
      products: [],
      stats: {},
      pagination: {
        page: 1,
        limit: 50,
        totalCount: 0,
        totalPages: 0,
        hasNext: false,
        hasPrev: false,
      }
    };
  }
}

// Get inventory statistics
export async function getInventoryStats(whereClause = {}) {
  try {
    const baseWhere = { is_active: true, ...whereClause };

    const [
      totalProducts,
      totalValue,
      lowStockCount,
      outOfStockCount,
      activeProducts,
      inactiveProducts
    ] = await Promise.all([
      // Total products count
      prisma.product.count({ where: baseWhere }),

      // Total inventory value
      prisma.product.aggregate({
        where: baseWhere,
        _sum: {
          price: true,
          buying_price: true,
        }
      }),

      // Low stock count (≤ 10)
      prisma.product.count({
        where: {
          ...baseWhere,
          product_quantity: { lte: 10, gt: 0 }
        }
      }),

      // Out of stock count
      prisma.product.count({
        where: {
          ...baseWhere,
          product_quantity: { lte: 0 }
        }
      }),

      // Active products
      prisma.product.count({
        where: { is_active: true }
      }),

      // Inactive products
      prisma.product.count({
        where: { is_active: false }
      })
    ]);

    // Calculate total stock quantity
    const stockData = await prisma.product.aggregate({
      where: baseWhere,
      _sum: {
        product_quantity: true
      }
    });

    return {
      totalProducts,
      totalStockQuantity: stockData._sum.product_quantity || 0,
      totalSellingValue: totalValue._sum.price || 0,
      totalBuyingValue: totalValue._sum.buying_price || 0,
      lowStockCount,
      outOfStockCount,
      activeProducts,
      inactiveProducts,
      profitMargin: totalValue._sum.price && totalValue._sum.buying_price
        ? ((totalValue._sum.price - totalValue._sum.buying_price) / totalValue._sum.price * 100).toFixed(2)
        : 0
    };
  } catch (error) {
    console.error('Error fetching inventory stats:', error);
    return {
      totalProducts: 0,
      totalStockQuantity: 0,
      totalSellingValue: 0,
      totalBuyingValue: 0,
      lowStockCount: 0,
      outOfStockCount: 0,
      activeProducts: 0,
      inactiveProducts: 0,
      profitMargin: 0
    };
  }
}

// Create new product
export async function createProduct(productData) {
  try {
    // Validate required fields
    if (!productData.name || !productData.sku || !productData.price) {
      throw new Error('Name, SKU, and price are required');
    }

    // Check if SKU already exists
    const existingSku = await prisma.product.findUnique({
      where: { sku: productData.sku }
    });

    if (existingSku) {
      throw new Error('Product with this SKU already exists');
    }

    const product = await prisma.product.create({
      data: {
        sku: productData.sku.trim(),
        name: productData.name.trim(),
        description: productData.description?.trim() || null,
        categoryId: productData.categoryId ? parseInt(productData.categoryId) : null,
        subCategoryId: productData.subCategoryId ? parseInt(productData.subCategoryId) : null,
        brand: productData.brand?.trim() || null,
        price: parseFloat(productData.price),
        buying_price: parseFloat(productData.buying_price) || 0,
        image_url: productData.image_url?.trim() || null,
        product_url: productData.product_url?.trim() || null,
        product_quantity: parseInt(productData.product_quantity) || 0,
        is_active: productData.is_active !== undefined ? productData.is_active : true,
        featured: productData.featured !== undefined ? productData.featured : false,
        sortOrder: parseInt(productData.sortOrder) || 0,
        supplierId: productData.supplierId ? parseInt(productData.supplierId) : null,
      },
      include: {
        category: true,
        subCategory: true,
        supplier: true,
      }
    });

    return { success: true, product };
  } catch (error) {
    console.error('Error creating product:', error);
    return {
      success: false,
      error: error.message || 'Failed to create product'
    };
  }
}

// Update product
export async function updateProduct(id, productData) {
  try {
    // Check if SKU already exists (excluding current product)
    if (productData.sku) {
      const existingSku = await prisma.product.findUnique({
        where: { sku: productData.sku }
      });

      if (existingSku && existingSku.id !== parseInt(id)) {
        throw new Error('Product with this SKU already exists');
      }
    }

    const updateData = {};

    // Only update provided fields
    if (productData.sku !== undefined) updateData.sku = productData.sku.trim();
    if (productData.name !== undefined) updateData.name = productData.name.trim();
    if (productData.description !== undefined) updateData.description = productData.description?.trim() || null;
    if (productData.categoryId !== undefined) updateData.categoryId = productData.categoryId ? parseInt(productData.categoryId) : null;
    if (productData.subCategoryId !== undefined) updateData.subCategoryId = productData.subCategoryId ? parseInt(productData.subCategoryId) : null;
    if (productData.brand !== undefined) updateData.brand = productData.brand?.trim() || null;
    if (productData.price !== undefined) updateData.price = parseFloat(productData.price);
    if (productData.buying_price !== undefined) updateData.buying_price = parseFloat(productData.buying_price);
    if (productData.image_url !== undefined) updateData.image_url = productData.image_url?.trim() || null;
    if (productData.product_url !== undefined) updateData.product_url = productData.product_url?.trim() || null;
    if (productData.product_quantity !== undefined) updateData.product_quantity = parseInt(productData.product_quantity);
    if (productData.is_active !== undefined) updateData.is_active = productData.is_active;
    if (productData.featured !== undefined) updateData.featured = productData.featured;
    if (productData.sortOrder !== undefined) updateData.sortOrder = parseInt(productData.sortOrder);
    if (productData.supplierId !== undefined) updateData.supplierId = productData.supplierId ? parseInt(productData.supplierId) : null;

    const product = await prisma.product.update({
      where: { id: parseInt(id) },
      data: updateData,
      include: {
        category: true,
        subCategory: true,
        supplier: true,
      }
    });

    return { success: true, product };
  } catch (error) {
    console.error('Error updating product:', error);
    return {
      success: false,
      error: error.message || 'Failed to update product'
    };
  }
}

// Delete product
export async function deleteProduct(id) {
  try {
    // Check if product is used in any orders, loans, or rentals
    const [orderItems, loanItems, rentalItems] = await Promise.all([
      prisma.orderItem.findFirst({ where: { productId: parseInt(id) } }),
      prisma.loanitem.findFirst({ where: { productId: parseInt(id) } }),
      prisma.rentalitem.findFirst({ where: { productId: parseInt(id) } })
    ]);

    if (orderItems || loanItems || rentalItems) {
      // Instead of deleting, deactivate the product
      const product = await prisma.product.update({
        where: { id: parseInt(id) },
        data: { is_active: false },
        include: {
          category: true,
          subCategory: true,
          supplier: true,
        }
      });

      return {
        success: true,
        product,
        message: 'Product deactivated instead of deleted due to existing transactions'
      };
    }

    await prisma.product.delete({
      where: { id: parseInt(id) }
    });

    return { success: true, message: 'Product deleted successfully' };
  } catch (error) {
    console.error('Error deleting product:', error);
    return {
      success: false,
      error: error.message || 'Failed to delete product'
    };
  }
}

// Bulk update products
export async function bulkUpdateProducts(updates) {
  try {
    const results = [];

    for (const update of updates) {
      const { id, ...updateData } = update;
      const result = await updateProduct(id, updateData);
      results.push({ id, ...result });
    }

    const successCount = results.filter(r => r.success).length;
    const failureCount = results.filter(r => !r.success).length;

    return {
      success: true,
      results,
      summary: {
        total: updates.length,
        successful: successCount,
        failed: failureCount
      }
    };
  } catch (error) {
    console.error('Error bulk updating products:', error);
    return {
      success: false,
      error: error.message || 'Failed to bulk update products'
    };
  }
}

// Bulk delete products
export async function bulkDeleteProducts(productIds) {
  try {
    const results = [];

    for (const id of productIds) {
      const result = await deleteProduct(id);
      results.push({ id, ...result });
    }

    const successCount = results.filter(r => r.success).length;
    const failureCount = results.filter(r => !r.success).length;

    return {
      success: true,
      results,
      summary: {
        total: productIds.length,
        successful: successCount,
        failed: failureCount
      }
    };
  } catch (error) {
    console.error('Error bulk deleting products:', error);
    return {
      success: false,
      error: error.message || 'Failed to bulk delete products'
    };
  }
}

// Stock adjustment
export async function adjustStock(productId, adjustmentData) {
  try {
    const { quantity, type, reason, notes } = adjustmentData;

    const product = await prisma.product.findUnique({
      where: { id: parseInt(productId) }
    });

    if (!product) {
      throw new Error('Product not found');
    }

    let newQuantity;
    if (type === 'increase') {
      newQuantity = product.product_quantity + parseInt(quantity);
    } else if (type === 'decrease') {
      newQuantity = Math.max(0, product.product_quantity - parseInt(quantity));
    } else if (type === 'set') {
      newQuantity = parseInt(quantity);
    } else {
      throw new Error('Invalid adjustment type');
    }

    const updatedProduct = await prisma.product.update({
      where: { id: parseInt(productId) },
      data: { product_quantity: newQuantity },
      include: {
        category: true,
        subCategory: true,
        supplier: true,
      }
    });

    // Log the stock adjustment (you might want to create a stock_adjustments table)
    console.log(`Stock adjustment: Product ${productId}, ${type} ${quantity}, Reason: ${reason}, Notes: ${notes}`);

    return {
      success: true,
      product: updatedProduct,
      adjustment: {
        previousQuantity: product.product_quantity,
        newQuantity,
        adjustmentQuantity: quantity,
        type,
        reason,
        notes
      }
    };
  } catch (error) {
    console.error('Error adjusting stock:', error);
    return {
      success: false,
      error: error.message || 'Failed to adjust stock'
    };
  }
}

// Get inventory movements/history
export async function getInventoryMovements(filters = {}) {
  try {
    // This would typically come from a stock_movements table
    // For now, we'll get data from orders, loans, and rentals

    const movements = [];

    // Get order movements
    const orderMovements = await prisma.orderItem.findMany({
      where: filters.productId ? { productId: parseInt(filters.productId) } : {},
      include: {
        order: {
          include: {
            customer: true
          }
        },
        product: true
      },
      orderBy: { createdAt: 'desc' },
      take: 100
    });

    orderMovements.forEach(item => {
      movements.push({
        id: `order-${item.id}`,
        type: 'sale',
        productId: item.productId,
        productName: item.product?.name || 'Unknown Product',
        quantity: -item.quantity, // Negative for outgoing
        reference: item.order?.orderNumber || 'Unknown Order',
        customer: item.order?.customer ?
          `${item.order.customer.firstName} ${item.order.customer.lastName || ''}`.trim() :
          'Unknown Customer',
        date: item.createdAt,
        notes: `Order sale - ${item.quantity} units`
      });
    });

    // Get loan movements
    const loanMovements = await prisma.loanitem.findMany({
      where: filters.productId ? { productId: parseInt(filters.productId) } : {},
      include: {
        loan: {
          include: {
            customer: true
          }
        },
        product: true
      },
      orderBy: { createdAt: 'desc' },
      take: 100
    });

    loanMovements.forEach(item => {
      movements.push({
        id: `loan-${item.id}`,
        type: 'loan',
        productId: item.productId,
        productName: item.product.name,
        quantity: -item.quantity, // Negative for outgoing
        reference: item.loan.loanNumber,
        customer: item.loan.customer.firstName + ' ' + (item.loan.customer.lastName || ''),
        date: item.createdAt,
        notes: `Loan - ${item.quantity} units`
      });
    });

    // Sort by date descending
    movements.sort((a, b) => new Date(b.date) - new Date(a.date));

    return {
      success: true,
      movements: movements.slice(0, filters.limit || 50)
    };
  } catch (error) {
    console.error('Error fetching inventory movements:', error);
    return {
      success: false,
      error: error.message || 'Failed to fetch inventory movements',
      movements: []
    };
  }
}

// Generate inventory report
export async function generateInventoryReport(filters = {}) {
  try {
    const { reportType = 'summary', dateFrom, dateTo } = filters;

    const report = {
      generatedAt: new Date(),
      reportType,
      filters,
      data: {}
    };

    switch (reportType) {
      case 'summary':
        report.data = await getInventoryStats();
        break;

      case 'low_stock':
        const lowStockProducts = await getLowStockProducts(filters.threshold || 10);
        report.data = {
          threshold: filters.threshold || 10,
          products: lowStockProducts,
          count: lowStockProducts.length
        };
        break;

      case 'out_of_stock':
        const outOfStockProducts = await getOutOfStockProducts();
        report.data = {
          products: outOfStockProducts,
          count: outOfStockProducts.length
        };
        break;

      case 'valuation':
        const valuationData = await prisma.product.findMany({
          where: { is_active: true },
          select: {
            id: true,
            name: true,
            sku: true,
            product_quantity: true,
            price: true,
            buying_price: true,
            category: { select: { name: true } },
            subCategory: { select: { name: true } }
          }
        });

        const totalSellingValue = valuationData.reduce((sum, p) => sum + (p.price * p.product_quantity), 0);
        const totalBuyingValue = valuationData.reduce((sum, p) => sum + (p.buying_price * p.product_quantity), 0);

        report.data = {
          products: valuationData,
          summary: {
            totalProducts: valuationData.length,
            totalSellingValue,
            totalBuyingValue,
            potentialProfit: totalSellingValue - totalBuyingValue,
            profitMargin: totalSellingValue > 0 ? ((totalSellingValue - totalBuyingValue) / totalSellingValue * 100).toFixed(2) : 0
          }
        };
        break;

      case 'movement':
        const movements = await getInventoryMovements(filters);
        report.data = movements;
        break;

      default:
        throw new Error('Invalid report type');
    }

    return { success: true, report };
  } catch (error) {
    console.error('Error generating inventory report:', error);
    return {
      success: false,
      error: error.message || 'Failed to generate inventory report'
    };
  }
}

// Import products from CSV/Excel data
export async function importProducts(productsData) {
  try {
    const results = [];
    const errors = [];

    for (let i = 0; i < productsData.length; i++) {
      const productData = productsData[i];

      try {
        // Validate required fields
        if (!productData.name || !productData.sku || !productData.price) {
          errors.push({
            row: i + 1,
            error: 'Missing required fields: name, sku, or price'
          });
          continue;
        }

        // Check if SKU already exists
        const existingSku = await prisma.product.findUnique({
          where: { sku: productData.sku }
        });

        if (existingSku) {
          errors.push({
            row: i + 1,
            sku: productData.sku,
            error: 'SKU already exists'
          });
          continue;
        }

        const result = await createProduct(productData);

        if (result.success) {
          results.push({
            row: i + 1,
            sku: productData.sku,
            name: productData.name,
            status: 'success'
          });
        } else {
          errors.push({
            row: i + 1,
            sku: productData.sku,
            error: result.error
          });
        }
      } catch (error) {
        errors.push({
          row: i + 1,
          sku: productData.sku || 'Unknown',
          error: error.message
        });
      }
    }

    return {
      success: true,
      summary: {
        total: productsData.length,
        successful: results.length,
        failed: errors.length
      },
      results,
      errors
    };
  } catch (error) {
    console.error('Error importing products:', error);
    return {
      success: false,
      error: error.message || 'Failed to import products'
    };
  }
}

// Export products to CSV format
export async function exportProducts(filters = {}) {
  try {
    const { products } = await getInventoryProducts({
      ...filters,
      limit: 10000 // Get all products for export
    });

    const csvData = products.map(product => ({
      SKU: product.sku,
      Name: product.name,
      Description: product.description || '',
      Category: product.category?.name || '',
      SubCategory: product.subCategory?.name || '',
      Brand: product.brand || '',
      Price: product.price,
      BuyingPrice: product.buying_price,
      Quantity: product.product_quantity,
      Status: product.is_active ? 'Active' : 'Inactive',
      Featured: product.featured ? 'Yes' : 'No',
      Supplier: product.supplier?.name || '',
      CreatedAt: product.createdAt,
      UpdatedAt: product.updatedAt
    }));

    return {
      success: true,
      data: csvData,
      filename: `inventory_export_${new Date().toISOString().split('T')[0]}.csv`
    };
  } catch (error) {
    console.error('Error exporting products:', error);
    return {
      success: false,
      error: error.message || 'Failed to export products'
    };
  }
}

// Helper function to get low stock products
export async function getLowStockProducts(threshold = 10) {
  try {
    const products = await prisma.product.findMany({
      where: {
        is_active: true,
        product_quantity: { lte: threshold, gt: 0 }
      },
      include: {
        category: true,
        subCategory: true,
        supplier: true,
      },
      orderBy: {
        product_quantity: 'asc'
      }
    });

    return products;
  } catch (error) {
    console.error('Error fetching low stock products:', error);
    return [];
  }
}

// Helper function to get out of stock products
export async function getOutOfStockProducts() {
  try {
    const products = await prisma.product.findMany({
      where: {
        is_active: true,
        product_quantity: { lte: 0 }
      },
      include: {
        category: true,
        subCategory: true,
        supplier: true,
      },
      orderBy: {
        name: 'asc'
      }
    });

    return products;
  } catch (error) {
    console.error('Error fetching out of stock products:', error);
    return [];
  }
}

// Get product by ID with full details
export async function getProductById(id) {
  try {
    const product = await prisma.product.findUnique({
      where: { id: parseInt(id) },
      include: {
        category: true,
        subCategory: true,
        supplier: true,
      }
    });

    if (!product) {
      return {
        success: false,
        error: 'Product not found'
      };
    }

    return { success: true, product };
  } catch (error) {
    console.error('Error fetching product:', error);
    return {
      success: false,
      error: error.message || 'Failed to fetch product'
    };
  }
}

// Get product by SKU
export async function getProductBySku(sku) {
  try {
    const product = await prisma.product.findUnique({
      where: { sku: sku.trim() },
      include: {
        category: true,
        subCategory: true,
        supplier: true,
      }
    });

    if (!product) {
      return {
        success: false,
        error: 'Product not found'
      };
    }

    return { success: true, product };
  } catch (error) {
    console.error('Error fetching product by SKU:', error);
    return {
      success: false,
      error: error.message || 'Failed to fetch product'
    };
  }
}

// Duplicate product
export async function duplicateProduct(id) {
  try {
    const originalProduct = await prisma.product.findUnique({
      where: { id: parseInt(id) }
    });

    if (!originalProduct) {
      throw new Error('Product not found');
    }

    // Generate new SKU
    const timestamp = Date.now().toString().slice(-6);
    const newSku = `${originalProduct.sku}-COPY-${timestamp}`;

    const duplicatedProduct = await prisma.product.create({
      data: {
        sku: newSku,
        name: `${originalProduct.name} (Copy)`,
        description: originalProduct.description,
        categoryId: originalProduct.categoryId,
        subCategoryId: originalProduct.subCategoryId,
        brand: originalProduct.brand,
        price: originalProduct.price,
        buying_price: originalProduct.buying_price,
        image_url: originalProduct.image_url,
        product_url: originalProduct.product_url,
        product_quantity: 0, // Start with 0 quantity for duplicated product
        is_active: false, // Start as inactive
        featured: false,
        sortOrder: originalProduct.sortOrder,
        supplierId: originalProduct.supplierId,
      },
      include: {
        category: true,
        subCategory: true,
        supplier: true,
      }
    });

    return { success: true, product: duplicatedProduct };
  } catch (error) {
    console.error('Error duplicating product:', error);
    return {
      success: false,
      error: error.message || 'Failed to duplicate product'
    };
  }
}
