
'use server';

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// Get all products with category and subcategory information
export async function getProducts(filters = {}) {
  try {
    const where = {};

    // Apply filters
    if (filters.category && filters.category !== 'all') {
      where.categoryId = parseInt(filters.category);
    }

    if (filters.subcategory && filters.subcategory !== 'all') {
      where.subCategoryId = parseInt(filters.subcategory);
    }

    if (filters.search) {
      where.OR = [
        { name: { contains: filters.search } },
        { sku: { contains: filters.search } },
        { brand: { contains: filters.search } }
      ];

    }

    if (filters.status === 'active') {
      where.is_active = true;
    } else if (filters.status === 'inactive') {
      where.is_active = false;
    }

    if (filters.stock === 'low') {
      where.product_quantity = {
        lte: 10,
        gt: 0,
      };
    } else if (filters.stock === 'out') {
      where.product_quantity = {
        lte: 0,
      };
    }

    // Pagination parameters
    const page = parseInt(filters.page) || 1;
    const limit = parseInt(filters.limit) || 20;
    const skip = (page - 1) * limit;

    // Get total count for pagination
    const totalCount = await prisma.product.count({ where });

    const products = await prisma.product.findMany({
      where,
      include: {
        category: true,
        subCategory: true,
        supplier: true,
      },
      orderBy: {
        name: 'asc',
      },
      skip,
      take: limit,
    });

    return {
      products,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        hasNext: page < Math.ceil(totalCount / limit),
        hasPrev: page > 1,
      }
    };
  } catch (error) {
    console.error('Error fetching products:', error);
    return {
      products: [],
      pagination: {
        page: 1,
        limit: 20,
        totalCount: 0,
        totalPages: 0,
        hasNext: false,
        hasPrev: false,
      }
    };
  }
}

// Get all categories
export async function getCategories() {
  try {
    const categories = await prisma.category.findMany({
      include: {
        subCategories: true,
        _count: {
          select: {
            products: true,
          },
        },
      },
      orderBy: {
        sortOrder: 'asc',
      },
    });

    return categories;
  } catch (error) {
    console.error('Error fetching categories:', error);
    return [];
  }
}

// Get subcategories by category ID
export async function getSubCategories(categoryId = null) {
  try {
    const where = {};

    if (categoryId) {
      where.categoryId = parseInt(categoryId);
    }

    const subcategories = await prisma.subCategory.findMany({
      where,
      include: {
        category: true,
        _count: {
          select: {
            products: true,
          },
        },
      },
      orderBy: {
        sortOrder: 'asc',
      },
    });

    return subcategories;
  } catch (error) {
    console.error('Error fetching subcategories:', error);
    return [];
  }
}

// Get product by ID
export async function getProductById(id) {
  try {
    const product = await prisma.product.findUnique({
      where: {
        id: parseInt(id),
      },
      include: {
        category: true,
        subCategory: true,
        supplier: true,
      },
    });

    return product;
  } catch (error) {
    console.error('Error fetching product:', error);
    return null;
  }
}

// Search products for POS
export async function searchProducts(query) {
  try {
    const products = await prisma.product.findMany({
      where: {
        AND: [
          { is_active: true },
          { product_quantity: { gt: 0 } },
          {
            OR: [
              { name: { contains: query } },
              { sku: { contains: query } },
              { brand: { contains: query } }
            ]
          }
        ]
      },
      include: {
        category: true,
        subCategory: true,
      },
      take: 20,
      orderBy: {
        name: 'asc',
      },
    });

    return products;
  } catch (error) {
    console.error('Error searching products:', error);
    return [];
  }
}

// Get products by category for POS
export async function getProductsByCategory(categoryId) {
  try {
    const products = await prisma.product.findMany({
      where: {
        categoryId: parseInt(categoryId),
        is_active: true,
        product_quantity: { gt: 0 },
      },
      include: {
        category: true,
        subCategory: true,
      },
      orderBy: {
        name: 'asc',
      },
    });

    return products;
  } catch (error) {
    console.error('Error fetching products by category:', error);
    return [];
  }
}

// Get products by subcategory for POS
export async function getProductsBySubCategory(subcategoryId) {
  try {
    const products = await prisma.product.findMany({
      where: {
        subCategoryId: parseInt(subcategoryId),
        is_active: true,
        product_quantity: { gt: 0 },
      },
      include: {
        category: true,
        subCategory: true,
      },
      orderBy: {
        name: 'asc',
      },
    });

    return products;
  } catch (error) {
    console.error('Error fetching products by subcategory:', error);
    return [];
  }
}

// ==================== CUSTOMER FUNCTIONS ====================

// Get all customers with pagination and search
export async function getCustomers(filters = {}) {
  try {
    const where = {};

    // Apply search filter
    if (filters.search) {
      where.OR = [
        { firstName: { contains: filters.search } },
        { lastName: { contains: filters.search } },
        { email: { contains: filters.search } },
        { phone: { contains: filters.search } },
        { cnic: { contains: filters.search } }
      ];
    }

    // Pagination parameters
    const page = parseInt(filters.page) || 1;
    const limit = parseInt(filters.limit) || 20;
    const skip = (page - 1) * limit;

    // Get total count for pagination
    const totalCount = await prisma.customer.count({ where });

    const customers = await prisma.customer.findMany({
      where,
      orderBy: {
        firstName: 'asc',
      },
      skip,
      take: limit,
    });

    return {
      customers,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        hasNext: page < Math.ceil(totalCount / limit),
        hasPrev: page > 1,
      }
    };
  } catch (error) {
    console.error('Error fetching customers:', error);
    return {
      customers: [],
      pagination: {
        page: 1,
        limit: 20,
        totalCount: 0,
        totalPages: 0,
        hasNext: false,
        hasPrev: false,
      }
    };
  }
}

// Get customer by ID
export async function getCustomerById(id) {
  try {
    const customer = await prisma.customer.findUnique({
      where: {
        id: parseInt(id),
      },
    });

    return customer;
  } catch (error) {
    console.error('Error fetching customer:', error);
    return null;
  }
}

// Search customers for POS (quick search)
export async function searchCustomers(query) {
  try {
    const customers = await prisma.customer.findMany({
      where: {
        OR: [
          { firstName: { contains: query } },
          { lastName: { contains: query } },
          { email: { contains: query } },
          { phone: { contains: query } },
          { cnic: { contains: query } }
        ]
      },
      orderBy: {
        firstName: 'asc',
      },
      take: 10, // Limit to 10 results for quick search
    });

    return customers;
  } catch (error) {
    console.error('Error searching customers:', error);
    return [];
  }
}

// Create a new customer
export async function createCustomer(customerData) {
  try {
    // Validate required fields
    if (!customerData.firstName || !customerData.email) {
      throw new Error('First name and email are required');
    }

    // Check if email already exists
    const existingCustomer = await prisma.customer.findUnique({
      where: { email: customerData.email }
    });

    if (existingCustomer) {
      throw new Error('Customer with this email already exists');
    }

    // Check if CNIC already exists (if provided)
    if (customerData.cnic) {
      const existingCnic = await prisma.customer.findUnique({
        where: { cnic: customerData.cnic }
      });

      if (existingCnic) {
        throw new Error('Customer with this CNIC already exists');
      }
    }

    const customer = await prisma.customer.create({
      data: {
        firstName: customerData.firstName.trim(),
        lastName: customerData.lastName?.trim() || null,
        email: customerData.email.trim().toLowerCase(),
        phone: customerData.phone?.trim() || null,
        cnic: customerData.cnic?.trim() || null,
      },
    });

    return { success: true, customer };
  } catch (error) {
    console.error('Error creating customer:', error);
    return {
      success: false,
      error: error.message || 'Failed to create customer'
    };
  }
}

// Update customer
export async function updateCustomer(id, customerData) {
  try {
    // Validate required fields
    if (!customerData.firstName || !customerData.email) {
      throw new Error('First name and email are required');
    }

    // Check if email already exists (excluding current customer)
    const existingCustomer = await prisma.customer.findUnique({
      where: { email: customerData.email }
    });

    if (existingCustomer && existingCustomer.id !== parseInt(id)) {
      throw new Error('Customer with this email already exists');
    }

    // Check if CNIC already exists (if provided and excluding current customer)
    if (customerData.cnic) {
      const existingCnic = await prisma.customer.findUnique({
        where: { cnic: customerData.cnic }
      });

      if (existingCnic && existingCnic.id !== parseInt(id)) {
        throw new Error('Customer with this CNIC already exists');
      }
    }

    const customer = await prisma.customer.update({
      where: { id: parseInt(id) },
      data: {
        firstName: customerData.firstName.trim(),
        lastName: customerData.lastName?.trim() || null,
        email: customerData.email.trim().toLowerCase(),
        phone: customerData.phone?.trim() || null,
        cnic: customerData.cnic?.trim() || null,
      },
    });

    return { success: true, customer };
  } catch (error) {
    console.error('Error updating customer:', error);
    return {
      success: false,
      error: error.message || 'Failed to update customer'
    };
  }
}

// Delete customer
export async function deleteCustomer(id) {
  try {
    await prisma.customer.delete({
      where: { id: parseInt(id) },
    });

    return { success: true };
  } catch (error) {
    console.error('Error deleting customer:', error);
    return {
      success: false,
      error: error.message || 'Failed to delete customer'
    };
  }
}

// Get customer by email
export async function getCustomerByEmail(email) {
  try {
    const customer = await prisma.customer.findUnique({
      where: {
        email: email.toLowerCase(),
      },
    });

    return customer;
  } catch (error) {
    console.error('Error fetching customer by email:', error);
    return null;
  }
}

// Get customer by phone
export async function getCustomerByPhone(phone) {
  try {
    const customer = await prisma.customer.findFirst({
      where: {
        phone: phone,
      },
    });

    return customer;
  } catch (error) {
    console.error('Error fetching customer by phone:', error);
    return null;
  }
}

// Get customer by CNIC
export async function getCustomerByCnic(cnic) {
  try {
    const customer = await prisma.customer.findUnique({
      where: {
        cnic: cnic,
      },
    });

    return customer;
  } catch (error) {
    console.error('Error fetching customer by CNIC:', error);
    return null;
  }
}

// Get recent customers (last 10)
export async function getRecentCustomers() {
  try {
    const customers = await prisma.customer.findMany({
      orderBy: {
        createdAt: 'desc',
      },
      take: 10,
    });

    return customers;
  } catch (error) {
    console.error('Error fetching recent customers:', error);
    return [];
  }
}
