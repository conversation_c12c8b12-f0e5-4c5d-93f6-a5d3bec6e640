'use client';

import { useState, useEffect } from 'react';
import { FiSearch, FiPlus, FiMinus, FiTrash2, FiUser, FiCreditCard, FiDollarSign } from 'react-icons/fi';
import DashboardLayout from '../components/DashboardLayout';
import Card from '../components/ui/Card';
import Button from '../components/ui/Button';
import { Input, Select } from '../components/ui/Form';
import Modal from '../components/ui/Modal';
import Price from '../components/ui/Price';
import toast from 'react-hot-toast';
import { getProducts } from './action'; // Assuming you have an API function to fetch products
// Mock data
const mockProducts = [
  {
    id: 1,
    name: "Sainbury's Corn Flakes 500g",
    category: "Cereals",
    price: 25.20,
    image: "https://springs.com.pk/cdn/shop/files/00358149.gif?v=1736781431",
    stock: 25
  },
  {
    id: 2,
    name: "Heaven's Smoked Honey 255g",
    category: "Honey & Spreads",
    price: 33.66,
    image: "https://springs.com.pk/cdn/shop/files/860006051617.gif?v=1736782085",
    stock: 15
  },
  {
    id: 3,
    name: "M&S Smooth Peanut Butter 340g",
    category: "Spreads",
    price: 25.20,
    image: "https://springs.com.pk/cdn/shop/files/819602.gif?v=1744628939",
    stock: 30
  },
  {
    id: 4,
    name: "Hersheys Nuggets Dark Chocolate 289g",
    category: "Chocolates",
    price: 31.60,
    image: "https://springs.com.pk/cdn/shop/files/images_3c693b99-2171-4519-8fd8-71fae707bc1e.jpg?v=1740217009",
    stock: 40
  },
  {
    id: 5,
    name: "Lindt Swiss Premium Chocolates 300g",
    category: "Chocolates",
    price: 40.00,
    image: "https://springs.com.pk/cdn/shop/files/7610400073752.gif?v=1744626339",
    stock: 15
  },
  {
    id: 6,
    name: "Cheetos Minis Flamin Hot Snacks 102.7g",
    category: "Snacks",
    price: 25.20,
    image: "https://springs.com.pk/cdn/shop/files/028400700108.gif?v=1742881742",
    stock: 50
  }
];

const mockCategories = [
  { id: 'all', name: 'All' },
  { id: 1, name: 'Cereals' },
  { id: 2, name: 'Honey & Spreads' },
  { id: 3, name: 'Spreads' },
  { id: 4, name: 'Chocolates' },
  { id: 5, name: 'Snacks' }
];

const mockCustomers = [
  {
    id: 1,
    name: 'John Doe',
    email: '<EMAIL>',
    phone: '+****************'
  },
  {
    id: 2,
    name: 'Jane Smith',
    email: '<EMAIL>',
    phone: '+****************'
  },
  {
    id: 3,
    name: 'Bob Johnson',
    email: '<EMAIL>',
    phone: '+****************'
  },
  {
    id: 4,
    name: 'Alice Brown',
    email: '<EMAIL>',
    phone: '+****************'
  }
];

export default function POSPage() {
  const [products, setProducts] = useState([]);
  const [filteredProducts, setFilteredProducts] = useState([]);
  const [categories, setCategories] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [searchQuery, setSearchQuery] = useState('');
  const [cart, setCart] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isCustomerModalOpen, setIsCustomerModalOpen] = useState(false);
  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState(null);
  const [paymentMethod, setPaymentMethod] = useState('CASH');
  const [amountReceived, setAmountReceived] = useState('');
  const [discount, setDiscount] = useState('');

  useEffect(() => {
    // Fetch products from the API or use mock data
    const fetchProducts = async () => {
      try {
        const data = await getProducts(); // Replace with your API call
        setProducts(data);
        setFilteredProducts(data);
      } catch (error) {
        console.error('Error fetching products:', error);
        toast.error('Failed to load products');
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchProducts();
  }
  , []);
  // Load data
  useEffect(() => {
    const timer = setTimeout(() => {
      setProducts(products);
      setFilteredProducts(products);
      setCategories(mockCategories);
      setIsLoading(false);
    }, 1000);
    
    return () => clearTimeout(timer);
  }, []);
  
  console.log('Products fetched:', getProducts);
  // Filter products by category and search query
  useEffect(() => {
    let filtered = [...products];

    if (selectedCategory !== 'All') {
      filtered = filtered.filter(product => product.category === selectedCategory);
    }

    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(query) ||
        product.category.toLowerCase().includes(query)
      );
    }

    setFilteredProducts(filtered);
  }, [selectedCategory, searchQuery, products]);

  // Calculate cart totals
  const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  const discountAmount = discount ? (subtotal * (parseFloat(discount) / 100)) : 0;
  const taxRate = 0.07; // 7% tax rate
  const taxAmount = (subtotal - discountAmount) * taxRate;
  const total = subtotal - discountAmount + taxAmount;
  const change = amountReceived ? parseFloat(amountReceived) - total : 0;

  // Add product to cart
  const addToCart = (product) => {
    const existingItem = cart.find(item => item.id === product.id);

    if (existingItem) {
      setCart(cart.map(item =>
        item.id === product.id
          ? { ...item, quantity: item.quantity + 1 }
          : item
      ));
    } else {
      setCart([...cart, { ...product, quantity: 1 }]);
    }
  };

  // Update cart item quantity
  const updateQuantity = (id, newQuantity) => {
    if (newQuantity < 1) return;

    setCart(cart.map(item =>
      item.id === id
        ? { ...item, quantity: newQuantity }
        : item
    ));
  };

  // Remove item from cart
  const removeFromCart = (id) => {
    setCart(cart.filter(item => item.id !== id));
  };

  // Clear cart
  const clearCart = () => {
    setCart([]);
    setSelectedCustomer(null);
    setDiscount('');
  };

  // Process payment
  const processPayment = () => {
    if (cart.length === 0) {
      toast.error('Cart is empty');
      return;
    }

    if (paymentMethod === 'CASH' && (!amountReceived || parseFloat(amountReceived) < total)) {
      toast.error('Amount received must be greater than or equal to the total');
      return;
    }

    // Here you would normally save the order to the database
    toast.success('Payment processed successfully!');

    // Print receipt or show receipt modal
    // ...

    // Clear cart and reset state
    clearCart();
    setPaymentMethod('CASH');
    setAmountReceived('');
    setIsPaymentModalOpen(false);
  };

  return (
    <DashboardLayout title="Point of Sale">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Products Section */}
        <div className="lg:col-span-2">
          <Card>
            <div className="mb-6 flex flex-col md:flex-row md:items-center md:justify-between gap-4">
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium text-gray-500">Category:</span>
                <div className="flex flex-wrap gap-2">
                  {categories.map(category => (
                    <button
                      key={category.id}
                      className={`px-3 py-1 text-sm rounded-full ${
                        selectedCategory === category.name
                          ? 'bg-primary text-white'
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      }`}
                      onClick={() => setSelectedCategory(category.name)}
                    >
                      {category.name}
                    </button>
                  ))}
                </div>
              </div>

              <div className="relative">
                <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search products..."
                  className="pl-10 pr-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>

            {isLoading ? (
              <div className="flex items-center justify-center h-64">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
              </div>
            ) : (
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
                {filteredProducts.map(product => (
                  <div
                    key={product.id}
                    className="border rounded-lg overflow-hidden hover:shadow-md transition-shadow cursor-pointer"
                    onClick={() => addToCart(product)}
                  >
                    <div className="aspect-square bg-gray-100 flex items-center justify-center">
                      <img
                        src={product.image}
                        alt={product.name}
                        className="object-cover w-full h-full"
                      />
                    </div>
                    <div className="p-3">
                      <h3 className="font-medium text-gray-800 truncate">{product.name}</h3>
                      <p className="text-sm text-gray-500">{product.category}</p>
                      <p className="font-bold text-primary mt-1"><Price amount={product.price} /></p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </Card>
        </div>

        {/* Cart Section */}
        <div>
          <Card className="sticky top-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-medium text-gray-800">Current Sale</h2>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setIsCustomerModalOpen(true)}
                >
                  <FiUser className="mr-1" /> Customer
                </Button>
                <Button
                  variant="danger"
                  size="sm"
                  onClick={clearCart}
                  disabled={cart.length === 0}
                >
                  Clear
                </Button>
              </div>
            </div>

            {selectedCustomer && (
              <div className="mb-4 p-3 bg-gray-50 rounded-lg">
                <div className="flex justify-between items-center">
                  <div>
                    <p className="font-medium">{selectedCustomer.name}</p>
                    <p className="text-sm text-gray-500">{selectedCustomer.email}</p>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setSelectedCustomer(null)}
                  >
                    Change
                  </Button>
                </div>
              </div>
            )}

            <div className="max-h-[calc(100vh-400px)] overflow-y-auto mb-4">
              {cart.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <p>No items in cart</p>
                  <p className="text-sm">Add products by clicking on them</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {cart.map(item => (
                    <div key={item.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center">
                        <img
                          src={item.image}
                          alt={item.name}
                          className="w-12 h-12 object-cover rounded mr-3"
                        />
                        <div>
                          <h4 className="font-medium">{item.name}</h4>
                          <p className="text-sm text-gray-500"><Price amount={item.price} /></p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <button
                          className="p-1 rounded-full hover:bg-gray-100"
                          onClick={() => updateQuantity(item.id, item.quantity - 1)}
                        >
                          <FiMinus size={16} />
                        </button>
                        <span className="w-8 text-center">{item.quantity}</span>
                        <button
                          className="p-1 rounded-full hover:bg-gray-100"
                          onClick={() => updateQuantity(item.id, item.quantity + 1)}
                        >
                          <FiPlus size={16} />
                        </button>
                        <button
                          className="p-1 rounded-full hover:bg-gray-100 text-red-500"
                          onClick={() => removeFromCart(item.id)}
                        >
                          <FiTrash2 size={16} />
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            <div className="border-t pt-4 space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-900">Subtotal</span>
                <span><Price amount={subtotal} /></span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-900">Discount</span>
                <div className="flex items-center">
                  <input
                    type="number"
                    min="0"
                    max="100"
                    placeholder="0%"
                    className="w-16 p-1 text-right border rounded"
                    value={discount}
                    onChange={(e) => setDiscount(e.target.value)}
                  />
                  <span className="ml-1">%</span>
                </div>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-900">Tax (7%)</span>
                <span><Price amount={taxAmount} /></span>
              </div>
              <div className="flex justify-between font-bold text-lg pt-2 border-t">
                <span>Total</span>
                <span><Price amount={total} /></span>
              </div>
            </div>

            <div className="mt-6">
              <Button
                className="w-full py-3 text-lg"
                disabled={cart.length === 0}
                onClick={() => setIsPaymentModalOpen(true)}
              >
                Proceed to Payment
              </Button>
            </div>
          </Card>
        </div>
      </div>

      {/* Customer Selection Modal */}
      <Modal
        isOpen={isCustomerModalOpen}
        onClose={() => setIsCustomerModalOpen(false)}
        title="Select Customer"
        size="md"
      >
        <div className="mb-4">
          <Input
            placeholder="Search customers..."
            prefix={<FiSearch />}
          />
        </div>

        <div className="space-y-3 max-h-96 overflow-y-auto">
          {mockCustomers.map(customer => (
            <div
              key={customer.id}
              className="p-3 border rounded-lg hover:bg-gray-50 cursor-pointer"
              onClick={() => {
                setSelectedCustomer(customer);
                setIsCustomerModalOpen(false);
              }}
            >
              <div className="flex justify-between">
                <div>
                  <h4 className="font-medium">{customer.name}</h4>
                  <p className="text-sm text-gray-700">{customer.email}</p>
                </div>
                <p className="text-sm text-gray-700">{customer.phone}</p>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-4 pt-4 border-t">
          <Button
            variant="outline"
            className="w-full"
          >
            <FiPlus className="mr-2" /> Add New Customer
          </Button>
        </div>
      </Modal>

      {/* Payment Modal */}
      <Modal
        isOpen={isPaymentModalOpen}
        onClose={() => setIsPaymentModalOpen(false)}
        title="Payment"
        size="md"
        footer={
          <>
            <Button
              variant="outline"
              onClick={() => setIsPaymentModalOpen(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={processPayment}
            >
              Complete Payment
            </Button>
          </>
        }
      >
        <div className="space-y-6">
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex justify-between mb-2">
              <span className="text-gray-900">Subtotal</span>
              <span><Price amount={subtotal} /></span>
            </div>
            {discount && (
              <div className="flex justify-between mb-2">
                <span className="text-gray-900">Discount ({discount}%)</span>
                <span>-<Price amount={discountAmount} /></span>
              </div>
            )}
            <div className="flex justify-between mb-2">
              <span className="text-gray-900">Tax (7%)</span>
              <span><Price amount={taxAmount} /></span>
            </div>
            <div className="flex justify-between font-bold text-lg pt-2 border-t">
              <span>Total</span>
              <span><Price amount={total} /></span>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-900 mb-2">
              Payment Method
            </label>
            <div className="grid grid-cols-3 gap-3">
              <button
                className={`flex flex-col items-center justify-center p-3 border rounded-lg ${
                  paymentMethod === 'CASH' ? 'bg-primary text-white border-primary' : 'hover:bg-gray-50'
                }`}
                onClick={() => setPaymentMethod('CASH')}
              >
                <FiDollarSign size={24} className="mb-1" />
                <span className="text-sm">Cash</span>
              </button>
              <button
                className={`flex flex-col items-center justify-center p-3 border rounded-lg ${
                  paymentMethod === 'CREDIT_CARD' ? 'bg-primary text-white border-primary' : 'hover:bg-gray-50'
                }`}
                onClick={() => setPaymentMethod('CREDIT_CARD')}
              >
                <FiCreditCard size={24} className="mb-1" />
                <span className="text-sm">Card</span>
              </button>
              <button
                className={`flex flex-col items-center justify-center p-3 border rounded-lg ${
                  paymentMethod === 'OTHER' ? 'bg-primary text-white border-primary' : 'hover:bg-gray-50'
                }`}
                onClick={() => setPaymentMethod('OTHER')}
              >
                <FiDollarSign size={24} className="mb-1" />
                <span className="text-sm">Other</span>
              </button>
            </div>
          </div>

          {paymentMethod === 'CASH' && (
            <div>
              <label className="block text-sm font-medium text-gray-900 mb-2">
                Amount Received
              </label>
              <Input
                type="number"
                min={total}
                step="0.01"
                value={amountReceived}
                onChange={(e) => setAmountReceived(e.target.value)}
              />

              {amountReceived && parseFloat(amountReceived) >= total && (
                <div className="mt-2 p-2 bg-green-50 text-green-700 rounded">
                  Change: <Price amount={change} />
                </div>
              )}
            </div>
          )}
        </div>
      </Modal>
    </DashboardLayout>
  );
}
