'use client';

import { useState, useEffect } from 'react';
import { FiDownload, FiPrinter, FiMail, FiBarChart2, <PERSON>PieChart, FiTrendingUp } from 'react-icons/fi';
import DashboardLayout from '../components/DashboardLayout';
import Card from '../components/ui/Card';
import Button from '../components/ui/Button';
import { Select, FormRow } from '../components/ui/Form';
import { Bar, Line, Pie } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import { jsPDF } from 'jspdf';
import 'jspdf-autotable';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend
);

// Mock data for demonstration
const mockSalesData = {
  daily: {
    labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
    datasets: [
      {
        label: 'Sales',
        data: [1200, 1900, 1500, 2500, 2200, 3000, 3500],
        backgroundColor: 'rgba(59, 130, 246, 0.5)',
        borderColor: 'rgb(59, 130, 246)',
        borderWidth: 1,
      },
    ],
  },
  weekly: {
    labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
    datasets: [
      {
        label: 'Sales',
        data: [10200, 8900, 12500, 15000],
        backgroundColor: 'rgba(59, 130, 246, 0.5)',
        borderColor: 'rgb(59, 130, 246)',
        borderWidth: 1,
      },
    ],
  },
  monthly: {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
    datasets: [
      {
        label: 'Sales',
        data: [25000, 28000, 32000, 30000, 35000, 38000, 40000, 42000, 45000, 48000, 50000, 55000],
        backgroundColor: 'rgba(59, 130, 246, 0.5)',
        borderColor: 'rgb(59, 130, 246)',
        borderWidth: 1,
      },
    ],
  },
};

const mockProductSalesData = {
  labels: ['T-Shirt', 'Jeans', 'Sneakers', 'Backpack', 'Watch', 'Sunglasses'],
  datasets: [
    {
      label: 'Units Sold',
      data: [120, 85, 65, 45, 30, 50],
      backgroundColor: [
        'rgba(255, 99, 132, 0.5)',
        'rgba(54, 162, 235, 0.5)',
        'rgba(255, 206, 86, 0.5)',
        'rgba(75, 192, 192, 0.5)',
        'rgba(153, 102, 255, 0.5)',
        'rgba(255, 159, 64, 0.5)',
      ],
      borderColor: [
        'rgba(255, 99, 132, 1)',
        'rgba(54, 162, 235, 1)',
        'rgba(255, 206, 86, 1)',
        'rgba(75, 192, 192, 1)',
        'rgba(153, 102, 255, 1)',
        'rgba(255, 159, 64, 1)',
      ],
      borderWidth: 1,
    },
  ],
};

const mockCategorySalesData = {
  labels: ['Clothing', 'Footwear', 'Accessories'],
  datasets: [
    {
      label: 'Sales',
      data: [15000, 8000, 12000],
      backgroundColor: [
        'rgba(54, 162, 235, 0.5)',
        'rgba(255, 206, 86, 0.5)',
        'rgba(75, 192, 192, 0.5)',
      ],
      borderColor: [
        'rgba(54, 162, 235, 1)',
        'rgba(255, 206, 86, 1)',
        'rgba(75, 192, 192, 1)',
      ],
      borderWidth: 1,
    },
  ],
};

const mockInventoryData = {
  labels: ['T-Shirt', 'Jeans', 'Sneakers', 'Backpack', 'Watch', 'Sunglasses'],
  datasets: [
    {
      label: 'Current Stock',
      data: [25, 15, 10, 12, 8, 20],
      backgroundColor: 'rgba(75, 192, 192, 0.5)',
      borderColor: 'rgb(75, 192, 192)',
      borderWidth: 1,
    },
    {
      label: 'Reorder Level',
      data: [10, 10, 5, 5, 5, 10],
      backgroundColor: 'rgba(255, 99, 132, 0.5)',
      borderColor: 'rgb(255, 99, 132)',
      borderWidth: 1,
    },
  ],
};

const mockProfitLossData = {
  labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
  datasets: [
    {
      label: 'Revenue',
      data: [25000, 28000, 32000, 30000, 35000, 38000],
      borderColor: 'rgb(59, 130, 246)',
      backgroundColor: 'rgba(59, 130, 246, 0.5)',
      tension: 0.3,
    },
    {
      label: 'Expenses',
      data: [18000, 19000, 22000, 21000, 24000, 26000],
      borderColor: 'rgb(255, 99, 132)',
      backgroundColor: 'rgba(255, 99, 132, 0.5)',
      tension: 0.3,
    },
    {
      label: 'Profit',
      data: [7000, 9000, 10000, 9000, 11000, 12000],
      borderColor: 'rgb(75, 192, 192)',
      backgroundColor: 'rgba(75, 192, 192, 0.5)',
      tension: 0.3,
    },
  ],
};

export default function ReportsPage() {
  const [activeTab, setActiveTab] = useState('sales');
  const [isLoading, setIsLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('monthly');
  const [salesData, setSalesData] = useState(mockSalesData.monthly);
  const [productSalesData, setProductSalesData] = useState(mockProductSalesData);
  const [categorySalesData, setCategorySalesData] = useState(mockCategorySalesData);
  const [inventoryData, setInventoryData] = useState(mockInventoryData);
  const [profitLossData, setProfitLossData] = useState(mockProfitLossData);

  // Load data
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  // Update sales data when time range changes
  useEffect(() => {
    setSalesData(mockSalesData[timeRange]);
  }, [timeRange]);

  // Chart options
  const barOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: true,
        text: 'Sales Report',
      },
    },
    scales: {
      y: {
        beginAtZero: true,
      },
    },
  };

  const pieOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: true,
        text: 'Sales by Category',
      },
    },
  };

  const lineOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: true,
        text: 'Profit & Loss',
      },
    },
    scales: {
      y: {
        beginAtZero: true,
      },
    },
  };

  // Generate PDF report
  const generatePDF = () => {
    const doc = new jsPDF();
    
    // Add title
    doc.setFontSize(18);
    doc.text('Sales Report', 105, 15, { align: 'center' });
    
    // Add date
    doc.setFontSize(12);
    doc.text(`Generated on: ${new Date().toLocaleDateString()}`, 105, 25, { align: 'center' });
    
    // Add sales data table
    const tableColumn = ['Period', 'Sales Amount'];
    const tableRows = [];
    
    salesData.labels.forEach((label, index) => {
      const amount = salesData.datasets[0].data[index];
      tableRows.push([label, `$${amount.toFixed(2)}`]);
    });
    
    doc.autoTable({
      head: [tableColumn],
      body: tableRows,
      startY: 35,
      theme: 'grid',
      styles: { fontSize: 10 },
      headStyles: { fillColor: [59, 130, 246] },
    });
    
    // Save the PDF
    doc.save('sales-report.pdf');
    
    toast.success('Report downloaded successfully');
  };

  return (
    <DashboardLayout title="Reports">
      <div className="mb-6">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 overflow-x-auto">
            <button
              className={`py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                activeTab === 'sales'
                  ? 'border-primary text-primary'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
              onClick={() => setActiveTab('sales')}
            >
              <FiBarChart2 className="inline mr-2" />
              Sales Report
            </button>
            <button
              className={`py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                activeTab === 'products'
                  ? 'border-primary text-primary'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
              onClick={() => setActiveTab('products')}
            >
              <FiPieChart className="inline mr-2" />
              Product Performance
            </button>
            <button
              className={`py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                activeTab === 'inventory'
                  ? 'border-primary text-primary'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
              onClick={() => setActiveTab('inventory')}
            >
              <FiBarChart2 className="inline mr-2" />
              Inventory Report
            </button>
            <button
              className={`py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                activeTab === 'profit'
                  ? 'border-primary text-primary'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
              onClick={() => setActiveTab('profit')}
            >
              <FiTrendingUp className="inline mr-2" />
              Profit & Loss
            </button>
          </nav>
        </div>
      </div>

      {isLoading ? (
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      ) : (
        <>
          {/* Sales Report */}
          {activeTab === 'sales' && (
            <div className="space-y-6">
              <Card>
                <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6 gap-4">
                  <div>
                    <h2 className="text-lg font-medium">Sales Report</h2>
                    <p className="text-sm text-gray-500">Overview of your sales performance</p>
                  </div>
                  
                  <div className="flex flex-col sm:flex-row gap-4">
                    <Select
                      value={timeRange}
                      onChange={(e) => setTimeRange(e.target.value)}
                      options={[
                        { value: 'daily', label: 'Daily' },
                        { value: 'weekly', label: 'Weekly' },
                        { value: 'monthly', label: 'Monthly' },
                      ]}
                      className="w-full sm:w-40"
                    />
                    
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        onClick={generatePDF}
                      >
                        <FiDownload className="mr-1" /> Download
                      </Button>
                      <Button
                        variant="outline"
                      >
                        <FiPrinter className="mr-1" /> Print
                      </Button>
                      <Button
                        variant="outline"
                      >
                        <FiMail className="mr-1" /> Email
                      </Button>
                    </div>
                  </div>
                </div>
                
                <div className="h-80">
                  <Bar options={barOptions} data={salesData} />
                </div>
              </Card>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card title="Top Selling Products">
                  <div className="h-64">
                    <Bar options={{
                      ...barOptions,
                      plugins: {
                        ...barOptions.plugins,
                        title: {
                          ...barOptions.plugins.title,
                          text: 'Top Selling Products',
                        },
                      },
                    }} data={productSalesData} />
                  </div>
                </Card>
                
                <Card title="Sales by Category">
                  <div className="h-64">
                    <Pie options={pieOptions} data={categorySalesData} />
                  </div>
                </Card>
              </div>
            </div>
          )}
          
          {/* Product Performance */}
          {activeTab === 'products' && (
            <Card>
              <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6 gap-4">
                <div>
                  <h2 className="text-lg font-medium">Product Performance</h2>
                  <p className="text-sm text-gray-500">Detailed analysis of product sales</p>
                </div>
                
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                  >
                    <FiDownload className="mr-1" /> Download
                  </Button>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="h-64">
                  <Bar options={{
                    ...barOptions,
                    plugins: {
                      ...barOptions.plugins,
                      title: {
                        ...barOptions.plugins.title,
                        text: 'Units Sold by Product',
                      },
                    },
                  }} data={productSalesData} />
                </div>
                
                <div className="h-64">
                  <Pie options={{
                    ...pieOptions,
                    plugins: {
                      ...pieOptions.plugins,
                      title: {
                        ...pieOptions.plugins.title,
                        text: 'Sales Distribution by Category',
                      },
                    },
                  }} data={categorySalesData} />
                </div>
              </div>
            </Card>
          )}
          
          {/* Inventory Report */}
          {activeTab === 'inventory' && (
            <Card>
              <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6 gap-4">
                <div>
                  <h2 className="text-lg font-medium">Inventory Report</h2>
                  <p className="text-sm text-gray-500">Current stock levels and reorder points</p>
                </div>
                
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                  >
                    <FiDownload className="mr-1" /> Download
                  </Button>
                </div>
              </div>
              
              <div className="h-80">
                <Bar options={{
                  ...barOptions,
                  plugins: {
                    ...barOptions.plugins,
                    title: {
                      ...barOptions.plugins.title,
                      text: 'Inventory Levels',
                    },
                  },
                }} data={inventoryData} />
              </div>
            </Card>
          )}
          
          {/* Profit & Loss */}
          {activeTab === 'profit' && (
            <Card>
              <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6 gap-4">
                <div>
                  <h2 className="text-lg font-medium">Profit & Loss</h2>
                  <p className="text-sm text-gray-500">Financial performance overview</p>
                </div>
                
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                  >
                    <FiDownload className="mr-1" /> Download
                  </Button>
                </div>
              </div>
              
              <div className="h-80">
                <Line options={lineOptions} data={profitLossData} />
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
                <div className="bg-green-50 p-4 rounded-lg">
                  <h3 className="text-sm font-medium text-green-800">Total Revenue</h3>
                  <p className="text-2xl font-bold text-green-600">$188,000</p>
                  <p className="text-sm text-green-500 mt-1">+12.5% from last period</p>
                </div>
                
                <div className="bg-red-50 p-4 rounded-lg">
                  <h3 className="text-sm font-medium text-red-800">Total Expenses</h3>
                  <p className="text-2xl font-bold text-red-600">$130,000</p>
                  <p className="text-sm text-red-500 mt-1">+8.3% from last period</p>
                </div>
                
                <div className="bg-blue-50 p-4 rounded-lg">
                  <h3 className="text-sm font-medium text-blue-800">Net Profit</h3>
                  <p className="text-2xl font-bold text-blue-600">$58,000</p>
                  <p className="text-sm text-blue-500 mt-1">+15.2% from last period</p>
                </div>
              </div>
            </Card>
          )}
        </>
      )}
    </DashboardLayout>
  );
}
