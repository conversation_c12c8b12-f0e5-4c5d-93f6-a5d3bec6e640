'use client';

import { useState, useEffect } from 'react';
import { <PERSON>Save, FiUser, FiShoppingBag, FiDollarSign, FiCreditCard, FiPrinter, FiServer, FiMail, FiGlobe } from 'react-icons/fi';
import DashboardLayout from '../components/DashboardLayout';
import Card from '../components/ui/Card';
import Button from '../components/ui/Button';
import { Input, Select, Textarea, Checkbox, FormGroup, FormRow } from '../components/ui/Form';
import toast from 'react-hot-toast';
import { useCurrency } from '../lib/currencyContext';

export default function SettingsPage() {
  const [activeTab, setActiveTab] = useState('general');
  const [isLoading, setIsLoading] = useState(true);

  // Use try-catch to handle the case when the currency context is not available
  let currencyContextValue = {
    currencies: [
      { code: 'USD', name: 'US Dollar', symbol: '$', rate: 1.00, isDefault: true },
      { code: 'EUR', name: 'Euro', symbol: '€', rate: 0.92, isDefault: false },
      { code: 'PKR', name: 'Pakistani Rupee', symbol: '₨', rate: 278.50, isDefault: false },
    ],
    enableMultipleCurrencies: true,
    updateCurrencySettings: () => {}
  };

  try {
    currencyContextValue = useCurrency();
  } catch (error) {
    console.error('Currency context not available:', error);
  }

  const { currencies, enableMultipleCurrencies, updateCurrencySettings } = currencyContextValue;
  const [generalSettings, setGeneralSettings] = useState({
    storeName: 'My POS Store',
    storeEmail: '<EMAIL>',
    storePhone: '************',
    storeAddress: '123 Main St, City, Country',
    currencySymbol: '$',
    taxRate: '7',
    logo: '',
  });
  const [userSettings, setUserSettings] = useState({
    name: 'Admin User',
    email: '<EMAIL>',
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });
  const [productSettings, setProductSettings] = useState({
    lowStockThreshold: '10',
    enableBarcodePrinting: true,
    defaultCategory: '1',
    showOutOfStock: true,
  });
  const [paymentSettings, setPaymentSettings] = useState({
    acceptCash: true,
    acceptCreditCard: true,
    acceptBankTransfer: false,
    acceptCheque: false,
    enablePartialPayments: true,
    defaultPaymentMethod: 'CASH',
  });
  const [receiptSettings, setReceiptSettings] = useState({
    showLogo: true,
    showTaxDetails: true,
    footerText: 'Thank you for your purchase!',
    printAutomatically: true,
    emailReceipt: false,
    receiptPrefix: 'INV-',
  });
  const [backupSettings, setBackupSettings] = useState({
    enableAutoBackup: true,
    backupFrequency: 'daily',
    backupTime: '00:00',
    backupLocation: 'local',
    emailBackupReports: false,
  });

  const [currencySettings, setCurrencySettings] = useState({
    defaultCurrency: currencies.find(c => c.isDefault)?.code || 'USD',
    enableMultipleCurrencies: enableMultipleCurrencies,
    currencies: currencies,
  });

  // Load settings
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  // Handle general settings changes
  const handleGeneralChange = (e) => {
    const { name, value } = e.target;
    setGeneralSettings({
      ...generalSettings,
      [name]: value,
    });
  };

  // Handle user settings changes
  const handleUserChange = (e) => {
    const { name, value } = e.target;
    setUserSettings({
      ...userSettings,
      [name]: value,
    });
  };

  // Handle product settings changes
  const handleProductChange = (e) => {
    const { name, value, type, checked } = e.target;
    setProductSettings({
      ...productSettings,
      [name]: type === 'checkbox' ? checked : value,
    });
  };

  // Handle payment settings changes
  const handlePaymentChange = (e) => {
    const { name, value, type, checked } = e.target;
    setPaymentSettings({
      ...paymentSettings,
      [name]: type === 'checkbox' ? checked : value,
    });
  };

  // Handle receipt settings changes
  const handleReceiptChange = (e) => {
    const { name, value, type, checked } = e.target;
    setReceiptSettings({
      ...receiptSettings,
      [name]: type === 'checkbox' ? checked : value,
    });
  };

  // Handle backup settings changes
  const handleBackupChange = (e) => {
    const { name, value, type, checked } = e.target;
    setBackupSettings({
      ...backupSettings,
      [name]: type === 'checkbox' ? checked : value,
    });
  };

  // Handle currency settings changes
  const handleCurrencyChange = (e) => {
    const { name, value, type, checked } = e.target;
    setCurrencySettings({
      ...currencySettings,
      [name]: type === 'checkbox' ? checked : value,
    });
  };

  // Handle currency rate changes
  const handleCurrencyRateChange = (code, field, value) => {
    setCurrencySettings({
      ...currencySettings,
      currencies: currencySettings.currencies.map(currency =>
        currency.code === code ? { ...currency, [field]: value } : currency
      ),
    });
  };

  // Set default currency
  const setDefaultCurrency = (code) => {
    setCurrencySettings({
      ...currencySettings,
      defaultCurrency: code,
      currencies: currencySettings.currencies.map(currency => ({
        ...currency,
        isDefault: currency.code === code,
      })),
    });
  };

  // Save settings
  const saveSettings = () => {
    // Here you would normally save to the database

    // If we're on the currency tab, update the currency context
    if (activeTab === 'currency') {
      updateCurrencySettings({
        currencies: currencySettings.currencies,
        enableMultipleCurrencies: currencySettings.enableMultipleCurrencies,
      });
    }

    toast.success('Settings saved successfully');
  };

  // Save user settings
  const saveUserSettings = () => {
    // Validate passwords
    if (userSettings.newPassword && userSettings.newPassword !== userSettings.confirmPassword) {
      toast.error('Passwords do not match');
      return;
    }

    // Here you would normally save to the database
    toast.success('User settings saved successfully');
  };

  return (
    <DashboardLayout title="Settings">
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {/* Settings Navigation */}
        <div className="md:col-span-1">
          <Card className="sticky top-6">
            <nav className="space-y-1">
              <button
                className={`flex items-center px-3 py-2 text-sm font-medium rounded-md w-full ${
                  activeTab === 'general'
                    ? 'bg-primary text-white'
                    : 'text-gray-700 hover:bg-gray-100'
                }`}
                onClick={() => setActiveTab('general')}
              >
                <FiShoppingBag className="mr-3 h-5 w-5" />
                General
              </button>
              <button
                className={`flex items-center px-3 py-2 text-sm font-medium rounded-md w-full ${
                  activeTab === 'user'
                    ? 'bg-primary text-white'
                    : 'text-gray-700 hover:bg-gray-100'
                }`}
                onClick={() => setActiveTab('user')}
              >
                <FiUser className="mr-3 h-5 w-5" />
                User Profile
              </button>
              <button
                className={`flex items-center px-3 py-2 text-sm font-medium rounded-md w-full ${
                  activeTab === 'products'
                    ? 'bg-primary text-white'
                    : 'text-gray-700 hover:bg-gray-100'
                }`}
                onClick={() => setActiveTab('products')}
              >
                <FiShoppingBag className="mr-3 h-5 w-5" />
                Products
              </button>
              <button
                className={`flex items-center px-3 py-2 text-sm font-medium rounded-md w-full ${
                  activeTab === 'payment'
                    ? 'bg-primary text-white'
                    : 'text-gray-700 hover:bg-gray-100'
                }`}
                onClick={() => setActiveTab('payment')}
              >
                <FiCreditCard className="mr-3 h-5 w-5" />
                Payment Methods
              </button>
              <button
                className={`flex items-center px-3 py-2 text-sm font-medium rounded-md w-full ${
                  activeTab === 'receipt'
                    ? 'bg-primary text-white'
                    : 'text-gray-700 hover:bg-gray-100'
                }`}
                onClick={() => setActiveTab('receipt')}
              >
                <FiPrinter className="mr-3 h-5 w-5" />
                Receipt
              </button>
              <button
                className={`flex items-center px-3 py-2 text-sm font-medium rounded-md w-full ${
                  activeTab === 'currency'
                    ? 'bg-primary text-white'
                    : 'text-gray-700 hover:bg-gray-100'
                }`}
                onClick={() => setActiveTab('currency')}
              >
                <FiGlobe className="mr-3 h-5 w-5" />
                Currency
              </button>
              <button
                className={`flex items-center px-3 py-2 text-sm font-medium rounded-md w-full ${
                  activeTab === 'backup'
                    ? 'bg-primary text-white'
                    : 'text-gray-700 hover:bg-gray-100'
                }`}
                onClick={() => setActiveTab('backup')}
              >
                <FiServer className="mr-3 h-5 w-5" />
                Backup & Restore
              </button>
            </nav>
          </Card>
        </div>

        {/* Settings Content */}
        <div className="md:col-span-3">
          {isLoading ? (
            <div className="flex items-center justify-center h-64">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
            </div>
          ) : (
            <>
              {/* General Settings */}
              {activeTab === 'general' && (
                <Card title="General Settings">
                  <FormGroup>
                    <Input
                      label="Store Name"
                      name="storeName"
                      value={generalSettings.storeName}
                      onChange={handleGeneralChange}
                      required
                    />

                    <FormRow>
                      <Input
                        label="Store Email"
                        name="storeEmail"
                        type="email"
                        value={generalSettings.storeEmail}
                        onChange={handleGeneralChange}
                      />
                      <Input
                        label="Store Phone"
                        name="storePhone"
                        value={generalSettings.storePhone}
                        onChange={handleGeneralChange}
                      />
                    </FormRow>

                    <Textarea
                      label="Store Address"
                      name="storeAddress"
                      value={generalSettings.storeAddress}
                      onChange={handleGeneralChange}
                    />

                    <FormRow>
                      <Input
                        label="Currency Symbol"
                        name="currencySymbol"
                        value={generalSettings.currencySymbol}
                        onChange={handleGeneralChange}
                      />
                      <Input
                        label="Tax Rate (%)"
                        name="taxRate"
                        type="number"
                        min="0"
                        max="100"
                        value={generalSettings.taxRate}
                        onChange={handleGeneralChange}
                      />
                    </FormRow>

                    <Input
                      label="Store Logo"
                      name="logo"
                      type="file"
                      onChange={handleGeneralChange}
                    />

                    <div className="flex justify-end mt-6">
                      <Button onClick={saveSettings}>
                        <FiSave className="mr-2" /> Save Settings
                      </Button>
                    </div>
                  </FormGroup>
                </Card>
              )}

              {/* User Profile */}
              {activeTab === 'user' && (
                <Card title="User Profile">
                  <FormGroup>
                    <FormRow>
                      <Input
                        label="Name"
                        name="name"
                        value={userSettings.name}
                        onChange={handleUserChange}
                        required
                      />
                      <Input
                        label="Email"
                        name="email"
                        type="email"
                        value={userSettings.email}
                        onChange={handleUserChange}
                        required
                      />
                    </FormRow>

                    <Input
                      label="Current Password"
                      name="currentPassword"
                      type="password"
                      value={userSettings.currentPassword}
                      onChange={handleUserChange}
                    />

                    <FormRow>
                      <Input
                        label="New Password"
                        name="newPassword"
                        type="password"
                        value={userSettings.newPassword}
                        onChange={handleUserChange}
                      />
                      <Input
                        label="Confirm Password"
                        name="confirmPassword"
                        type="password"
                        value={userSettings.confirmPassword}
                        onChange={handleUserChange}
                      />
                    </FormRow>

                    <div className="flex justify-end mt-6">
                      <Button onClick={saveUserSettings}>
                        <FiSave className="mr-2" /> Save Profile
                      </Button>
                    </div>
                  </FormGroup>
                </Card>
              )}

              {/* Product Settings */}
              {activeTab === 'products' && (
                <Card title="Product Settings">
                  <FormGroup>
                    <Input
                      label="Low Stock Threshold"
                      name="lowStockThreshold"
                      type="number"
                      min="0"
                      value={productSettings.lowStockThreshold}
                      onChange={handleProductChange}
                    />

                    <Select
                      label="Default Category"
                      name="defaultCategory"
                      value={productSettings.defaultCategory}
                      onChange={handleProductChange}
                      options={[
                        { value: '1', label: 'Clothing' },
                        { value: '2', label: 'Footwear' },
                        { value: '3', label: 'Accessories' },
                      ]}
                    />

                    <Checkbox
                      label="Enable Barcode Printing"
                      name="enableBarcodePrinting"
                      checked={productSettings.enableBarcodePrinting}
                      onChange={handleProductChange}
                    />

                    <Checkbox
                      label="Show Out of Stock Products"
                      name="showOutOfStock"
                      checked={productSettings.showOutOfStock}
                      onChange={handleProductChange}
                    />

                    <div className="flex justify-end mt-6">
                      <Button onClick={saveSettings}>
                        <FiSave className="mr-2" /> Save Settings
                      </Button>
                    </div>
                  </FormGroup>
                </Card>
              )}

              {/* Payment Settings */}
              {activeTab === 'payment' && (
                <Card title="Payment Method Settings">
                  <FormGroup>
                    <div className="space-y-4">
                      <Checkbox
                        label="Accept Cash Payments"
                        name="acceptCash"
                        checked={paymentSettings.acceptCash}
                        onChange={handlePaymentChange}
                      />

                      <Checkbox
                        label="Accept Credit Card Payments"
                        name="acceptCreditCard"
                        checked={paymentSettings.acceptCreditCard}
                        onChange={handlePaymentChange}
                      />

                      <Checkbox
                        label="Accept Bank Transfer"
                        name="acceptBankTransfer"
                        checked={paymentSettings.acceptBankTransfer}
                        onChange={handlePaymentChange}
                      />

                      <Checkbox
                        label="Accept Cheque"
                        name="acceptCheque"
                        checked={paymentSettings.acceptCheque}
                        onChange={handlePaymentChange}
                      />

                      <Checkbox
                        label="Enable Partial Payments"
                        name="enablePartialPayments"
                        checked={paymentSettings.enablePartialPayments}
                        onChange={handlePaymentChange}
                      />
                    </div>

                    <Select
                      label="Default Payment Method"
                      name="defaultPaymentMethod"
                      value={paymentSettings.defaultPaymentMethod}
                      onChange={handlePaymentChange}
                      options={[
                        { value: 'CASH', label: 'Cash' },
                        { value: 'CREDIT_CARD', label: 'Credit Card' },
                        { value: 'BANK_TRANSFER', label: 'Bank Transfer' },
                        { value: 'CHEQUE', label: 'Cheque' },
                      ]}
                    />

                    <div className="flex justify-end mt-6">
                      <Button onClick={saveSettings}>
                        <FiSave className="mr-2" /> Save Settings
                      </Button>
                    </div>
                  </FormGroup>
                </Card>
              )}

              {/* Receipt Settings */}
              {activeTab === 'receipt' && (
                <Card title="Receipt Settings">
                  <FormGroup>
                    <Checkbox
                      label="Show Logo on Receipt"
                      name="showLogo"
                      checked={receiptSettings.showLogo}
                      onChange={handleReceiptChange}
                    />

                    <Checkbox
                      label="Show Tax Details"
                      name="showTaxDetails"
                      checked={receiptSettings.showTaxDetails}
                      onChange={handleReceiptChange}
                    />

                    <Textarea
                      label="Receipt Footer Text"
                      name="footerText"
                      value={receiptSettings.footerText}
                      onChange={handleReceiptChange}
                    />

                    <Input
                      label="Receipt Number Prefix"
                      name="receiptPrefix"
                      value={receiptSettings.receiptPrefix}
                      onChange={handleReceiptChange}
                    />

                    <Checkbox
                      label="Print Receipt Automatically"
                      name="printAutomatically"
                      checked={receiptSettings.printAutomatically}
                      onChange={handleReceiptChange}
                    />

                    <Checkbox
                      label="Email Receipt to Customer"
                      name="emailReceipt"
                      checked={receiptSettings.emailReceipt}
                      onChange={handleReceiptChange}
                    />

                    <div className="flex justify-end mt-6">
                      <Button onClick={saveSettings}>
                        <FiSave className="mr-2" /> Save Settings
                      </Button>
                    </div>
                  </FormGroup>
                </Card>
              )}

              {/* Currency Settings */}
              {activeTab === 'currency' && (
                <Card title="Currency Settings">
                  <FormGroup>
                    <Checkbox
                      label="Enable Multiple Currencies"
                      name="enableMultipleCurrencies"
                      checked={currencySettings.enableMultipleCurrencies}
                      onChange={handleCurrencyChange}
                    />

                    <Select
                      label="Default Currency"
                      name="defaultCurrency"
                      value={currencySettings.defaultCurrency}
                      onChange={(e) => setDefaultCurrency(e.target.value)}
                      options={currencySettings.currencies.map(currency => ({
                        value: currency.code,
                        label: `${currency.name} (${currency.symbol})`,
                      }))}
                    />

                    <div className="mt-6">
                      <h3 className="text-lg font-medium text-gray-900 mb-4">Currency Exchange Rates</h3>
                      <p className="text-sm text-gray-500 mb-4">Set exchange rates relative to your default currency ({currencySettings.currencies.find(c => c.isDefault)?.symbol})</p>

                      <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                        <table className="min-w-full divide-y divide-gray-300">
                          <thead className="bg-gray-50">
                            <tr>
                              <th scope="col" className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">Currency</th>
                              <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Code</th>
                              <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Symbol</th>
                              <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Exchange Rate</th>
                              <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Default</th>
                            </tr>
                          </thead>
                          <tbody className="divide-y divide-gray-200 bg-white">
                            {currencySettings.currencies.map((currency) => (
                              <tr key={currency.code}>
                                <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6">{currency.name}</td>
                                <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">{currency.code}</td>
                                <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">{currency.symbol}</td>
                                <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                  <input
                                    type="number"
                                    min="0"
                                    step="0.01"
                                    className="w-24 rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm"
                                    value={currency.rate}
                                    onChange={(e) => handleCurrencyRateChange(currency.code, 'rate', parseFloat(e.target.value))}
                                    disabled={currency.isDefault}
                                  />
                                </td>
                                <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                  <input
                                    type="radio"
                                    name="defaultCurrency"
                                    checked={currency.isDefault}
                                    onChange={() => setDefaultCurrency(currency.code)}
                                    className="h-4 w-4 border-gray-300 text-primary focus:ring-primary"
                                  />
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </div>

                    <div className="flex justify-end mt-6">
                      <Button onClick={saveSettings}>
                        <FiSave className="mr-2" /> Save Settings
                      </Button>
                    </div>
                  </FormGroup>
                </Card>
              )}

              {/* Backup Settings */}
              {activeTab === 'backup' && (
                <Card title="Backup & Restore Settings">
                  <FormGroup>
                    <Checkbox
                      label="Enable Automatic Backups"
                      name="enableAutoBackup"
                      checked={backupSettings.enableAutoBackup}
                      onChange={handleBackupChange}
                    />

                    <Select
                      label="Backup Frequency"
                      name="backupFrequency"
                      value={backupSettings.backupFrequency}
                      onChange={handleBackupChange}
                      options={[
                        { value: 'daily', label: 'Daily' },
                        { value: 'weekly', label: 'Weekly' },
                        { value: 'monthly', label: 'Monthly' },
                      ]}
                    />

                    <Input
                      label="Backup Time"
                      name="backupTime"
                      type="time"
                      value={backupSettings.backupTime}
                      onChange={handleBackupChange}
                    />

                    <Select
                      label="Backup Location"
                      name="backupLocation"
                      value={backupSettings.backupLocation}
                      onChange={handleBackupChange}
                      options={[
                        { value: 'local', label: 'Local Storage' },
                        { value: 'cloud', label: 'Cloud Storage' },
                      ]}
                    />

                    <Checkbox
                      label="Email Backup Reports"
                      name="emailBackupReports"
                      checked={backupSettings.emailBackupReports}
                      onChange={handleBackupChange}
                    />

                    <div className="flex justify-between mt-6">
                      <Button
                        variant="outline"
                      >
                        Restore from Backup
                      </Button>
                      <Button
                        onClick={saveSettings}
                      >
                        <FiSave className="mr-2" /> Save Settings
                      </Button>
                    </div>
                  </FormGroup>
                </Card>
              )}
            </>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}
