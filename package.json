{"name": "pos", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "seed": "node prisma/seed.js"}, "dependencies": {"@prisma/client": "^6.8.2", "chart.js": "^4.4.9", "date-fns": "^4.1.0", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "next": "15.3.2", "prisma": "^6.8.2", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4"}}