datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
}

// ========== PRODUCTS ==========

model Category {
  id          Int       @id @default(autoincrement())
  name        String    @unique
  description String?
  sortOrder   Int       @default(0)
  parentId    Int?
  parent      Category? @relation("CategoryToCategory", fields: [parentId], references: [id])
  children    Category[] @relation("CategoryToCategory")
  products    Product[]
  subCategories SubCategory[]
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
}

model SubCategory {
  id          Int       @id @default(autoincrement())
  name        String
  description String?
  sortOrder   Int       @default(0)
  category    Category  @relation(fields: [categoryId], references: [id])
  categoryId  Int
  product    Product[]
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  @@unique([name, categoryId])
}

model Product {
  id               Int       @id @default(autoincrement())
  sku              String    @unique
  name             String
  description      String?
  
  category         Category? @relation(fields: [categoryId], references: [id])
  categoryId       Int?
  subCategory     SubCategory? @relation(fields: [subCategoryId], references: [id])
  subCategoryId   Int?
  brand            String?
  price            Float
  buying_price     Float
  image_url        String?
  product_url      String?
  product_quantity Int       @default(0)
  is_active        Boolean   @default(true)
  featured         Boolean   @default(false)
  sortOrder        Int       @default(0)
  supplier         Supplier? @relation(fields: [supplierId], references: [id])
  supplierId       Int?
  createdAt        DateTime  @default(now())
  updatedAt        DateTime  @updatedAt
  OrderItems       OrderItem[]
  StockMovements   StockMovement[]
  Discounts        Discount[]
  inventoryItems   InventoryItem[]
  purchaseOrderItems PurchaseOrderItem[]
  inventoryReceiptItems InventoryReceiptItem[]
  inventoryCountItems InventoryCountItem[]
}

// ========== SUPPLIERS ==========

model Supplier {
  id          Int       @id @default(autoincrement())
  name        String    @unique
  contactName String?
  email       String?
  phone       String?
  address     Address?
  addressId   Int?      @unique
  products    Product[]
  createdAt   DateTime  @default(now())
  purchaseOrders PurchaseOrder[]
}

// ========== CUSTOMERS ==========

model Customer {
  id           Int       @id @default(autoincrement())
  firstName    String
  lastName     String?
  email        String    @unique
  phone        String?
  addresses    Address[]
  orders       Order[]
  createdAt    DateTime  @default(now())
}

// ========== ADDRESSES ==========

model Address {
  id          Int       @id @default(autoincrement())
  street      String
  city        String
  state       String?
  postalCode  String?
  country     String
  customer    Customer? @relation(fields: [customerId], references: [id])
  customerId  Int?
  supplier    Supplier? @relation(fields: [supplierId], references: [id])
  supplierId  Int?      @unique
  createdAt   DateTime  @default(now())
}

// ========== ORDERS ==========

model Order {
  id            Int          @id @default(autoincrement())
  orderNumber   String       @unique
  customer      Customer?    @relation(fields: [customerId], references: [id])
  customerId    Int?
  status        OrderStatus  @default(PENDING)
  totalAmount   Float
  taxAmount     Float
  discountAmount Float
  payment       Payment?
  orderItems    OrderItem[]
  shippingInfo  ShippingInfo?
  createdAt     DateTime     @default(now())
  updatedAt     DateTime     @updatedAt
}

model OrderItem {
  id          Int      @id @default(autoincrement())
  order       Order    @relation(fields: [orderId], references: [id])
  orderId     Int
  product     Product  @relation(fields: [productId], references: [id])
  productId   Int
  quantity    Int
  unitPrice   Float
  discount    Float    @default(0)
  totalPrice  Float
}

// ========== PAYMENTS ==========

model Payment {
  id          Int       @id @default(autoincrement())
  order       Order     @relation(fields: [orderId], references: [id])
  orderId     Int       @unique
  method      PaymentMethod
  status      PaymentStatus
  amount      Float
  paidAt      DateTime?
  createdAt   DateTime  @default(now())
}

enum PaymentMethod {
  CASH
  CREDIT_CARD
  DEBIT_CARD
  PAYPAL
  BANK_TRANSFER
  OTHER
}

enum PaymentStatus {
  PENDING
  COMPLETED
  FAILED
  REFUNDED
}

// ========== SHIPPING ==========

model ShippingInfo {
  id          Int      @id @default(autoincrement())
  order       Order    @relation(fields: [orderId], references: [id])
  orderId     Int      @unique
  carrier     String
  trackingNo  String?
  shippedAt   DateTime?
  deliveredAt DateTime?
  status      ShippingStatus
}

enum ShippingStatus {
  PENDING
  SHIPPED
  DELIVERED
  RETURNED
  CANCELLED
}

// ========== INVENTORY MANAGEMENT ==========

model Warehouse {
  id          Int       @id @default(autoincrement())
  name        String    @unique
  location    String?
  description String?
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  inventoryItems InventoryItem[]
  stockMovements StockMovement[]
  inventoryCounts InventoryCount[]
}

model InventoryItem {
  id          Int       @id @default(autoincrement())
  product     Product   @relation(fields: [productId], references: [id])
  productId   Int
  warehouse   Warehouse @relation(fields: [warehouseId], references: [id])
  warehouseId Int
  quantity    Int       @default(0)
  minQuantity Int       @default(5)  // Reorder point
  maxQuantity Int?      // Maximum stock level
  location    String?   // Shelf/bin location within warehouse
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  stockMovements StockMovement[]

  @@unique([productId, warehouseId])
}

model StockMovement {
  id            Int       @id @default(autoincrement())
  product       Product   @relation(fields: [productId], references: [id])
  productId     Int
  warehouse     Warehouse @relation(fields: [warehouseId], references: [id])
  warehouseId   Int
  inventoryItem InventoryItem @relation(fields: [inventoryItemId], references: [id])
  inventoryItemId Int
  quantity      Int
  movementType  StockMovementType
  reason        String?
  reference     String?   // PO number, SO number, etc.
  notes         String?
  performedBy   User?     @relation(fields: [userId], references: [id])
  userId        Int?
  createdAt     DateTime  @default(now())
}

model PurchaseOrder {
  id            Int       @id @default(autoincrement())
  poNumber      String    @unique
  supplier      Supplier  @relation(fields: [supplierId], references: [id])
  supplierId    Int
  orderDate     DateTime  @default(now())
  expectedDate  DateTime?
  status        PurchaseOrderStatus @default(DRAFT)
  notes         String?
  createdBy     User?     @relation(fields: [userId], references: [id])
  userId        Int?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  items         PurchaseOrderItem[]
  receipts      InventoryReceipt[]
}

model PurchaseOrderItem {
  id            Int       @id @default(autoincrement())
  purchaseOrder PurchaseOrder @relation(fields: [purchaseOrderId], references: [id])
  purchaseOrderId Int
  product       Product   @relation(fields: [productId], references: [id])
  productId     Int
  quantity      Int
  unitPrice     Float
  receivedQuantity Int    @default(0)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
}

model InventoryReceipt {
  id            Int       @id @default(autoincrement())
  receiptNumber String    @unique
  purchaseOrder PurchaseOrder @relation(fields: [purchaseOrderId], references: [id])
  purchaseOrderId Int
  receiptDate   DateTime  @default(now())
  notes         String?
  receivedBy    User?     @relation(fields: [userId], references: [id])
  userId        Int?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  items         InventoryReceiptItem[]
}

model InventoryReceiptItem {
  id            Int       @id @default(autoincrement())
  receipt       InventoryReceipt @relation(fields: [receiptId], references: [id])
  receiptId     Int
  product       Product   @relation(fields: [productId], references: [id])
  productId     Int
  quantity      Int
  notes         String?
  createdAt     DateTime  @default(now())
}

model InventoryCount {
  id            Int       @id @default(autoincrement())
  countNumber   String    @unique
  warehouse     Warehouse @relation(fields: [warehouseId], references: [id])
  warehouseId   Int
  countDate     DateTime  @default(now())
  status        InventoryCountStatus @default(DRAFT)
  notes         String?
  countedBy     User?     @relation(fields: [userId], references: [id])
  userId        Int?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  items         InventoryCountItem[]
}

model InventoryCountItem {
  id            Int       @id @default(autoincrement())
  inventoryCount InventoryCount @relation(fields: [inventoryCountId], references: [id])
  inventoryCountId Int
  product       Product   @relation(fields: [productId], references: [id])
  productId     Int
  expectedQuantity Int
  actualQuantity Int
  notes         String?
  createdAt     DateTime  @default(now())
}

enum StockMovementType {
  PURCHASE
  SALE
  RETURN_FROM_CUSTOMER
  RETURN_TO_SUPPLIER
  TRANSFER
  ADJUSTMENT
  INVENTORY_COUNT
  DAMAGED
  EXPIRED
  WRITE_OFF
}

enum PurchaseOrderStatus {
  DRAFT
  SUBMITTED
  APPROVED
  PARTIALLY_RECEIVED
  RECEIVED
  CANCELLED
}

enum InventoryCountStatus {
  DRAFT
  IN_PROGRESS
  COMPLETED
  CANCELLED
}

// ========== DISCOUNTS & COUPONS ==========

model Discount {
  id          Int       @id @default(autoincrement())
  code        String    @unique
  description String?
  amount      Float?    // flat discount amount
  percentage  Float?    // or percentage discount (0-100)
  active      Boolean   @default(true)
  startDate   DateTime?
  endDate     DateTime?
  products    Product[]
}

// ========== TAX MANAGEMENT ==========

model TaxRate {
  id          Int       @id @default(autoincrement())
  name        String    @unique
  rate        Float     // Tax rate percentage (e.g., 7.5 for 7.5%)
  description String?
  isDefault   Boolean   @default(false)
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  TaxCategories TaxCategory[]
}

model TaxCategory {
  id          Int       @id @default(autoincrement())
  name        String    @unique
  description String?
  taxRate     TaxRate   @relation(fields: [taxRateId], references: [id])
  taxRateId   Int
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
}

// ========== ACCOUNTING ==========

model Account {
  id          Int       @id @default(autoincrement())
  name        String    @unique
  accountType AccountType
  description String?
  balance     Float     @default(0)
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  Transactions TransactionAccount[]
}

enum AccountType {
  ASSET
  LIABILITY
  EQUITY
  REVENUE
  EXPENSE
}

model Transaction {
  id            Int       @id @default(autoincrement())
  transactionNo String    @unique
  description   String?
  amount        Float
  transactionType TransactionType
  transactionDate DateTime
  reference     String?
  notes         String?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  accounts      TransactionAccount[]
}

model TransactionAccount {
  id            Int       @id @default(autoincrement())
  transaction   Transaction @relation(fields: [transactionId], references: [id])
  transactionId Int
  account       Account   @relation(fields: [accountId], references: [id])
  accountId     Int
  amount        Float
  isDebit       Boolean
  createdAt     DateTime  @default(now())

  @@unique([transactionId, accountId, isDebit])
}

enum TransactionType {
  SALE
  PURCHASE
  EXPENSE
  INCOME
  TRANSFER
  ADJUSTMENT
}

model FiscalYear {
  id          Int       @id @default(autoincrement())
  name        String    @unique
  startDate   DateTime
  endDate     DateTime
  isClosed    Boolean   @default(false)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
}

model User {
  id                Int       @id @default(autoincrement())
  username          String    @unique
  email             String    @unique
  password          String
  role              UserRole  @default(USER)
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt
  stockMovements    StockMovement[]
  purchaseOrders    PurchaseOrder[]
  inventoryReceipts InventoryReceipt[]
  inventoryCounts   InventoryCount[]
}

enum UserRole {
  ADMIN
  MANAGER
  CASHIER
  USER
  ACCOUNTANT
}

enum OrderStatus {
  PENDING
  PROCESSING
  COMPLETED
  CANCELLED
  REFUNDED
}
