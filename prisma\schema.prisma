generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model discounttoproduct {
  A Int
  B Int

  @@unique([A, B], map: "_DiscountToProduct_AB_unique")
  @@index([B], map: "_DiscountToProduct_B_index")
  @@map("_discounttoproduct")
}

model account {
  id          Int                 @id @default(autoincrement())
  name        String              @unique(map: "Account_name_key")
  accountType account_accountType
  description String?
  balance     Float               @default(0)
  isActive    Boolean             @default(true)
  createdAt   DateTime            @default(now())
  updatedAt   DateTime
}

model address {
  id         Int      @id @default(autoincrement())
  street     String
  city       String
  state      String?
  postalCode String?
  country    String
  customerId Int?
  supplierId Int?     @unique(map: "Address_supplierId_key")
  createdAt  DateTime @default(now())
}

model Category {
  id          Int       @id @default(autoincrement())
  name        String    @unique(map: "Category_name_key")
  description String?
  sortOrder   Int       @default(0)
  parentId    Int?
  parent      Category? @relation("CategoryToCategory", fields: [parentId], references: [id])
  children    Category[] @relation("CategoryToCategory")
  products    Product[]
  subCategories SubCategory[]
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  @@map("category")
}

model customer {
  id        Int      @id @default(autoincrement())
  firstName String
  lastName  String?
  email     String   @unique(map: "Customer_email_key")
  phone     String?
  createdAt DateTime @default(now())
}

model discount {
  id          Int       @id @default(autoincrement())
  code        String    @unique(map: "Discount_code_key")
  description String?
  amount      Float?
  percentage  Float?
  active      Boolean   @default(true)
  startDate   DateTime?
  endDate     DateTime?
}

model fiscalyear {
  id        Int      @id @default(autoincrement())
  name      String   @unique(map: "FiscalYear_name_key")
  startDate DateTime
  endDate   DateTime
  isClosed  Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime
}

model inventorycount {
  id          Int                   @id @default(autoincrement())
  countNumber String                @unique(map: "InventoryCount_countNumber_key")
  warehouseId Int
  countDate   DateTime              @default(now())
  status      inventorycount_status @default(DRAFT)
  notes       String?
  userId      Int?
  createdAt   DateTime              @default(now())
  updatedAt   DateTime
}

model inventorycountitem {
  id               Int      @id @default(autoincrement())
  inventoryCountId Int
  productId        Int
  expectedQuantity Int
  actualQuantity   Int
  notes            String?
  createdAt        DateTime @default(now())
}

model inventoryitem {
  id          Int      @id @default(autoincrement())
  productId   Int
  warehouseId Int
  quantity    Int      @default(0)
  minQuantity Int      @default(5)
  maxQuantity Int?
  location    String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime

  @@unique([productId, warehouseId], map: "InventoryItem_productId_warehouseId_key")
}

model inventoryreceipt {
  id              Int      @id @default(autoincrement())
  receiptNumber   String   @unique(map: "InventoryReceipt_receiptNumber_key")
  purchaseOrderId Int
  receiptDate     DateTime @default(now())
  notes           String?
  userId          Int?
  createdAt       DateTime @default(now())
  updatedAt       DateTime
}

model inventoryreceiptitem {
  id        Int      @id @default(autoincrement())
  receiptId Int
  productId Int
  quantity  Int
  notes     String?
  createdAt DateTime @default(now())
}

model order {
  id             Int          @id @default(autoincrement())
  orderNumber    String       @unique(map: "Order_orderNumber_key")
  customerId     Int?
  status         order_status @default(PENDING)
  totalAmount    Float
  taxAmount      Float
  discountAmount Float
  createdAt      DateTime     @default(now())
  updatedAt      DateTime
}

model orderitem {
  id         Int   @id @default(autoincrement())
  orderId    Int
  productId  Int
  quantity   Int
  unitPrice  Float
  discount   Float @default(0)
  totalPrice Float
}

model payment {
  id        Int            @id @default(autoincrement())
  orderId   Int            @unique(map: "Payment_orderId_key")
  method    payment_method
  status    payment_status
  amount    Float
  paidAt    DateTime?
  createdAt DateTime       @default(now())
}

model Product {
  id               Int       @id @default(autoincrement())
  sku              String    @unique(map: "Product_sku_key")
  name             String
  description      String?

  category         Category? @relation(fields: [categoryId], references: [id])
  categoryId       Int?
  subCategory     SubCategory? @relation(fields: [subCategoryId], references: [id])
  subCategoryId    Int?
  brand            String?
  price            Float
  buying_price     Float
  image_url        String?
  product_url      String?
  product_quantity Int       @default(0)
  is_active        Boolean   @default(true)
  featured         Boolean   @default(false)
  sortOrder        Int       @default(0)
  supplier         Supplier? @relation(fields: [supplierId], references: [id])
  supplierId       Int?
  createdAt        DateTime  @default(now())
  updatedAt        DateTime  @updatedAt

  @@map("product")
}

model purchaseorder {
  id           Int                  @id @default(autoincrement())
  poNumber     String               @unique(map: "PurchaseOrder_poNumber_key")
  supplierId   Int
  orderDate    DateTime             @default(now())
  expectedDate DateTime?
  status       purchaseorder_status @default(DRAFT)
  notes        String?
  userId       Int?
  createdAt    DateTime             @default(now())
  updatedAt    DateTime
}

model purchaseorderitem {
  id               Int      @id @default(autoincrement())
  purchaseOrderId  Int
  productId        Int
  quantity         Int
  unitPrice        Float
  receivedQuantity Int      @default(0)
  createdAt        DateTime @default(now())
  updatedAt        DateTime
}

model shippinginfo {
  id          Int                 @id @default(autoincrement())
  orderId     Int                 @unique(map: "ShippingInfo_orderId_key")
  carrier     String
  trackingNo  String?
  shippedAt   DateTime?
  deliveredAt DateTime?
  status      shippinginfo_status
}

model stockmovement {
  id              Int                        @id @default(autoincrement())
  productId       Int
  warehouseId     Int
  inventoryItemId Int
  quantity        Int
  movementType    stockmovement_movementType
  reason          String?
  reference       String?
  notes           String?
  userId          Int?
  createdAt       DateTime                   @default(now())
}

model SubCategory {
  id          Int       @id @default(autoincrement())
  name        String
  description String?
  sortOrder   Int       @default(0)
  category    Category  @relation(fields: [categoryId], references: [id])
  categoryId  Int
  products    Product[]
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  @@unique([name, categoryId])
  @@map("subcategory")
}

model Supplier {
  id          Int       @id @default(autoincrement())
  name        String    @unique(map: "Supplier_name_key")
  contactName String?
  email       String?
  phone       String?
  addressId   Int?      @unique(map: "Supplier_addressId_key")
  products    Product[]
  createdAt   DateTime  @default(now())

  @@map("supplier")
}

model taxcategory {
  id          Int      @id @default(autoincrement())
  name        String   @unique(map: "TaxCategory_name_key")
  description String?
  taxRateId   Int
  createdAt   DateTime @default(now())
  updatedAt   DateTime
}

model taxrate {
  id          Int      @id @default(autoincrement())
  name        String   @unique(map: "TaxRate_name_key")
  rate        Float
  description String?
  isDefault   Boolean  @default(false)
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime
}

model transaction {
  id              Int                         @id @default(autoincrement())
  transactionNo   String                      @unique(map: "Transaction_transactionNo_key")
  description     String?
  amount          Float
  transactionType transaction_transactionType
  transactionDate DateTime
  reference       String?
  notes           String?
  createdAt       DateTime                    @default(now())
  updatedAt       DateTime
}

model transactionaccount {
  id            Int      @id @default(autoincrement())
  transactionId Int
  accountId     Int
  amount        Float
  isDebit       Boolean
  createdAt     DateTime @default(now())

  @@unique([transactionId, accountId, isDebit], map: "TransactionAccount_transactionId_accountId_isDebit_key")
}

model user {
  id        Int       @id @default(autoincrement())
  username  String    @unique(map: "User_username_key")
  email     String    @unique(map: "User_email_key")
  password  String
  role      user_role @default(USER)
  createdAt DateTime  @default(now())
  updatedAt DateTime
}

model warehouse {
  id          Int      @id @default(autoincrement())
  name        String   @unique(map: "Warehouse_name_key")
  location    String?
  description String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime
}

enum account_accountType {
  ASSET
  LIABILITY
  EQUITY
  REVENUE
  EXPENSE
}

enum payment_method {
  CASH
  CREDIT_CARD
  DEBIT_CARD
  PAYPAL
  BANK_TRANSFER
  OTHER
}

enum order_status {
  PENDING
  PROCESSING
  COMPLETED
  CANCELLED
  REFUNDED
}

enum payment_status {
  PENDING
  COMPLETED
  FAILED
  REFUNDED
}

enum inventorycount_status {
  DRAFT
  IN_PROGRESS
  COMPLETED
  CANCELLED
}

enum transaction_transactionType {
  SALE
  PURCHASE
  EXPENSE
  INCOME
  TRANSFER
  ADJUSTMENT
}

enum user_role {
  ADMIN
  MANAGER
  CASHIER
  USER
  ACCOUNTANT
}

enum purchaseorder_status {
  DRAFT
  SUBMITTED
  APPROVED
  PARTIALLY_RECEIVED
  RECEIVED
  CANCELLED
}

enum stockmovement_movementType {
  PURCHASE
  SALE
  RETURN_FROM_CUSTOMER
  RETURN_TO_SUPPLIER
  TRANSFER
  ADJUSTMENT
  INVENTORY_COUNT
  DAMAGED
  EXPIRED
  WRITE_OFF
}

enum shippinginfo_status {
  PENDING
  SHIPPED
  DELIVERED
  RETURNED
  CANCELLED
}
