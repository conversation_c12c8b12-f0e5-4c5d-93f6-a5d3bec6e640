const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  console.log('Starting database seeding...');

  // Clear existing data
  console.log('Clearing existing data...');
  await clearDatabase();

  // Create users
  console.log('Creating users...');
  const users = await createUsers();

  // Create categories
  console.log('Creating categories...');
  const categories = await createCategories();

  // Create suppliers
  console.log('Creating suppliers...');
  const suppliers = await createSuppliers();

  // Create products
  console.log('Creating products...');
  const products = await createProducts(categories, suppliers);

  // Create customers
  console.log('Creating customers...');
  const customers = await createCustomers();

  // Create orders
  console.log('Creating orders...');
  const orders = await createOrders(customers, products, users);

  // Create stock movements
  console.log('Creating stock movements...');
  await createStockMovements(products, users);

  // Create accounts
  console.log('Creating accounts...');
  const accounts = await createAccounts();

  // Create transactions
  console.log('Creating transactions...');
  await createTransactions(accounts, orders);

  // Create tax rates and categories
  console.log('Creating tax rates and categories...');
  await createTaxRatesAndCategories();

  console.log('Database seeding completed successfully!');
}

async function clearDatabase() {
  // Delete data in reverse order of dependencies
  await prisma.transactionAccount.deleteMany();
  await prisma.transaction.deleteMany();
  await prisma.taxCategory.deleteMany();
  await prisma.taxRate.deleteMany();
  await prisma.stockMovement.deleteMany();
  await prisma.orderItem.deleteMany();
  await prisma.order.deleteMany();
  await prisma.product.deleteMany();
  await prisma.category.deleteMany();
  await prisma.supplier.deleteMany();
  await prisma.customer.deleteMany();
  await prisma.address.deleteMany();
  await prisma.account.deleteMany();
  await prisma.fiscalYear.deleteMany();
  await prisma.user.deleteMany();
}

async function createUsers() {
  const users = [
    {
      username: 'admin',
      email: '<EMAIL>',
      password: '$2a$10$GQf5xJ0zjF7LCCMqZBZ.WOovqQK.Fm8uJVT7.7MBNxrLSsZ0OPsVS', // hashed 'password'
      role: 'ADMIN',
    },
    {
      username: 'manager',
      email: '<EMAIL>',
      password: '$2a$10$GQf5xJ0zjF7LCCMqZBZ.WOovqQK.Fm8uJVT7.7MBNxrLSsZ0OPsVS',
      role: 'MANAGER',
    },
    {
      username: 'cashier',
      email: '<EMAIL>',
      password: '$2a$10$GQf5xJ0zjF7LCCMqZBZ.WOovqQK.Fm8uJVT7.7MBNxrLSsZ0OPsVS',
      role: 'CASHIER',
    },
    {
      username: 'accountant',
      email: '<EMAIL>',
      password: '$2a$10$GQf5xJ0zjF7LCCMqZBZ.WOovqQK.Fm8uJVT7.7MBNxrLSsZ0OPsVS',
      role: 'ACCOUNTANT',
    },
  ];

  const createdUsers = [];
  for (const user of users) {
    const createdUser = await prisma.user.create({
      data: user,
    });
    createdUsers.push(createdUser);
  }

  return createdUsers;
}

async function createCategories() {
  const categories = [
    {
      name: 'Clothing',
      description: 'Apparel and clothing items',
    },
    {
      name: 'Footwear',
      description: 'Shoes, sandals, and other footwear',
    },
    {
      name: 'Accessories',
      description: 'Bags, belts, and other accessories',
    },
    {
      name: 'Electronics',
      description: 'Electronic devices and gadgets',
    },
    {
      name: 'Home & Kitchen',
      description: 'Home and kitchen appliances and items',
    },
  ];

  const createdCategories = [];
  for (const category of categories) {
    const createdCategory = await prisma.category.create({
      data: category,
    });
    createdCategories.push(createdCategory);
  }

  // Create subcategories
  const subCategories = [
    {
      name: 'T-Shirts',
      description: 'T-shirts and tops',
      parentId: createdCategories[0].id, // Clothing
    },
    {
      name: 'Jeans',
      description: 'Denim jeans and pants',
      parentId: createdCategories[0].id, // Clothing
    },
    {
      name: 'Sneakers',
      description: 'Athletic and casual sneakers',
      parentId: createdCategories[1].id, // Footwear
    },
    {
      name: 'Bags',
      description: 'Backpacks, handbags, and other bags',
      parentId: createdCategories[2].id, // Accessories
    },
    {
      name: 'Smartphones',
      description: 'Mobile phones and smartphones',
      parentId: createdCategories[3].id, // Electronics
    },
  ];

  for (const subCategory of subCategories) {
    const createdSubCategory = await prisma.category.create({
      data: subCategory,
    });
    createdCategories.push(createdSubCategory);
  }

  return createdCategories;
}

async function createSuppliers() {
  const suppliers = [
    {
      name: 'Fashion Wholesale Inc.',
      contactName: 'John Smith',
      email: '<EMAIL>',
      phone: '************',
    },
    {
      name: 'Tech Suppliers Ltd.',
      contactName: 'Jane Doe',
      email: '<EMAIL>',
      phone: '************',
    },
    {
      name: 'Home Goods Supply Co.',
      contactName: 'Bob Johnson',
      email: '<EMAIL>',
      phone: '************',
    },
  ];

  const createdSuppliers = [];
  for (const supplier of suppliers) {
    // Create address first
    const address = await prisma.address.create({
      data: {
        street: `${Math.floor(Math.random() * 1000) + 1} Main St`,
        city: ['New York', 'Los Angeles', 'Chicago', 'Houston', 'Phoenix'][Math.floor(Math.random() * 5)],
        state: ['NY', 'CA', 'IL', 'TX', 'AZ'][Math.floor(Math.random() * 5)],
        postalCode: `${Math.floor(Math.random() * 90000) + 10000}`,
        country: 'USA',
      },
    });

    // Create supplier with address
    const createdSupplier = await prisma.supplier.create({
      data: {
        ...supplier,
        addressId: address.id,
      },
    });
    createdSuppliers.push(createdSupplier);
  }

  return createdSuppliers;
}

async function createProducts(categories, suppliers) {
  const products = [
    {
      sku: 'TSH001',
      name: 'Basic T-Shirt',
      description: 'Comfortable cotton t-shirt',
      categoryId: categories.find(c => c.name === 'T-Shirts').id,
      brand: 'FashionBrand',
      price: 19.99,
      buying_price: 10.00,
      product_quantity: 100,
      is_active: true,
      supplierId: suppliers[0].id,
    },
    {
      sku: 'JNS001',
      name: 'Slim Fit Jeans',
      description: 'Stylish slim fit jeans',
      categoryId: categories.find(c => c.name === 'Jeans').id,
      brand: 'DenimCo',
      price: 49.99,
      buying_price: 25.00,
      product_quantity: 75,
      is_active: true,
      supplierId: suppliers[0].id,
    },
    {
      sku: 'SNK001',
      name: 'Running Sneakers',
      description: 'Lightweight running shoes',
      categoryId: categories.find(c => c.name === 'Sneakers').id,
      brand: 'SportyFeet',
      price: 79.99,
      buying_price: 40.00,
      product_quantity: 50,
      is_active: true,
      supplierId: suppliers[0].id,
    },
    {
      sku: 'BKP001',
      name: 'Backpack',
      description: 'Durable backpack with multiple compartments',
      categoryId: categories.find(c => c.name === 'Bags').id,
      brand: 'PackIt',
      price: 39.99,
      buying_price: 20.00,
      product_quantity: 60,
      is_active: true,
      supplierId: suppliers[0].id,
    },
    {
      sku: 'SPH001',
      name: 'Smartphone X',
      description: 'Latest smartphone with advanced features',
      categoryId: categories.find(c => c.name === 'Smartphones').id,
      brand: 'TechBrand',
      price: 699.99,
      buying_price: 500.00,
      product_quantity: 25,
      is_active: true,
      supplierId: suppliers[1].id,
    },
    {
      sku: 'KTL001',
      name: 'Electric Kettle',
      description: 'Fast-boiling electric kettle',
      categoryId: categories.find(c => c.name === 'Home & Kitchen').id,
      brand: 'HomeAppliances',
      price: 29.99,
      buying_price: 15.00,
      product_quantity: 40,
      is_active: true,
      supplierId: suppliers[2].id,
    },
    {
      sku: 'TSH002',
      name: 'Graphic T-Shirt',
      description: 'T-shirt with graphic print',
      categoryId: categories.find(c => c.name === 'T-Shirts').id,
      brand: 'FashionBrand',
      price: 24.99,
      buying_price: 12.50,
      product_quantity: 80,
      is_active: true,
      supplierId: suppliers[0].id,
    },
    {
      sku: 'JNS002',
      name: 'Relaxed Fit Jeans',
      description: 'Comfortable relaxed fit jeans',
      categoryId: categories.find(c => c.name === 'Jeans').id,
      brand: 'DenimCo',
      price: 44.99,
      buying_price: 22.50,
      product_quantity: 65,
      is_active: true,
      supplierId: suppliers[0].id,
    },
    {
      sku: 'SNK002',
      name: 'Casual Sneakers',
      description: 'Everyday casual sneakers',
      categoryId: categories.find(c => c.name === 'Sneakers').id,
      brand: 'ComfySteps',
      price: 59.99,
      buying_price: 30.00,
      product_quantity: 45,
      is_active: true,
      supplierId: suppliers[0].id,
    },
    {
      sku: 'BKP002',
      name: 'Messenger Bag',
      description: 'Stylish messenger bag',
      categoryId: categories.find(c => c.name === 'Bags').id,
      brand: 'PackIt',
      price: 34.99,
      buying_price: 17.50,
      product_quantity: 55,
      is_active: true,
      supplierId: suppliers[0].id,
    },
  ];

  const createdProducts = [];
  for (const product of products) {
    const createdProduct = await prisma.product.create({
      data: product,
    });
    createdProducts.push(createdProduct);
  }

  return createdProducts;
}

async function createCustomers() {
  const customers = [
    {
      firstName: 'Alice',
      lastName: 'Johnson',
      email: '<EMAIL>',
      phone: '************',
    },
    {
      firstName: 'Bob',
      lastName: 'Smith',
      email: '<EMAIL>',
      phone: '************',
    },
    {
      firstName: 'Charlie',
      lastName: 'Brown',
      email: '<EMAIL>',
      phone: '************',
    },
    {
      firstName: 'Diana',
      lastName: 'Miller',
      email: '<EMAIL>',
      phone: '************',
    },
    {
      firstName: 'Edward',
      lastName: 'Wilson',
      email: '<EMAIL>',
      phone: '************',
    },
  ];

  const createdCustomers = [];
  for (const customer of customers) {
    // Create address first
    const address = await prisma.address.create({
      data: {
        street: `${Math.floor(Math.random() * 1000) + 1} Main St`,
        city: ['New York', 'Los Angeles', 'Chicago', 'Houston', 'Phoenix'][Math.floor(Math.random() * 5)],
        state: ['NY', 'CA', 'IL', 'TX', 'AZ'][Math.floor(Math.random() * 5)],
        postalCode: `${Math.floor(Math.random() * 90000) + 10000}`,
        country: 'USA',
      },
    });

    // Create customer with address
    const createdCustomer = await prisma.customer.create({
      data: {
        ...customer,
        addressId: address.id,
      },
    });
    createdCustomers.push(createdCustomer);
  }

  return createdCustomers;
}

async function createOrders(customers, products, users) {
  const orderStatuses = ['PENDING', 'PROCESSING', 'COMPLETED', 'CANCELLED'];
  const paymentMethods = ['CASH', 'CREDIT_CARD', 'DEBIT_CARD'];
  
  const orders = [];
  
  // Create 20 orders
  for (let i = 0; i < 20; i++) {
    const customer = customers[Math.floor(Math.random() * customers.length)];
    const status = orderStatuses[Math.floor(Math.random() * orderStatuses.length)];
    const paymentMethod = paymentMethods[Math.floor(Math.random() * paymentMethods.length)];
    const cashier = users.find(u => u.role === 'CASHIER');
    
    // Create between 1 and 5 order items
    const orderItems = [];
    const numItems = Math.floor(Math.random() * 5) + 1;
    const selectedProducts = [];
    
    for (let j = 0; j < numItems; j++) {
      // Select a random product that hasn't been added to this order yet
      let product;
      do {
        product = products[Math.floor(Math.random() * products.length)];
      } while (selectedProducts.includes(product.id));
      
      selectedProducts.push(product.id);
      
      const quantity = Math.floor(Math.random() * 3) + 1;
      const price = product.price;
      
      orderItems.push({
        productId: product.id,
        quantity: quantity,
        price: price,
      });
    }
    
    // Calculate order total
    const subtotal = orderItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    const tax = subtotal * 0.07; // 7% tax
    const total = subtotal + tax;
    
    // Create the order
    const order = await prisma.order.create({
      data: {
        customerId: customer.id,
        orderDate: new Date(Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000), // Random date in the last 30 days
        status: status,
        subtotal: subtotal,
        tax: tax,
        total: total,
        paymentMethod: paymentMethod,
        userId: cashier.id,
        OrderItems: {
          create: orderItems,
        },
      },
      include: {
        OrderItems: true,
      },
    });
    
    orders.push(order);
  }
  
  return orders;
}

async function createStockMovements(products, users) {
  const movementTypes = ['PURCHASE', 'SALE', 'ADJUSTMENT'];
  const stockMovements = [];
  
  // Create 30 stock movements
  for (let i = 0; i < 30; i++) {
    const product = products[Math.floor(Math.random() * products.length)];
    const movementType = movementTypes[Math.floor(Math.random() * movementTypes.length)];
    const user = users[Math.floor(Math.random() * users.length)];
    
    // Determine quantity based on movement type
    let quantity;
    if (movementType === 'PURCHASE') {
      quantity = Math.floor(Math.random() * 20) + 5; // 5-25 units
    } else if (movementType === 'SALE') {
      quantity = -(Math.floor(Math.random() * 5) + 1); // -1 to -5 units
    } else {
      // Adjustment can be positive or negative
      quantity = Math.floor(Math.random() * 10) - 5; // -5 to 5 units
    }
    
    const stockMovement = await prisma.stockMovement.create({
      data: {
        productId: product.id,
        quantity: quantity,
        movementType: movementType,
        reason: `${movementType} of ${Math.abs(quantity)} units`,
        userId: user.id,
      },
    });
    
    stockMovements.push(stockMovement);
  }
  
  return stockMovements;
}

async function createAccounts() {
  const accounts = [
    {
      name: 'Cash',
      accountType: 'ASSET',
      description: 'Cash on hand',
      balance: 5000.00,
    },
    {
      name: 'Bank Account',
      accountType: 'ASSET',
      description: 'Main bank account',
      balance: 25000.00,
    },
    {
      name: 'Accounts Receivable',
      accountType: 'ASSET',
      description: 'Money owed by customers',
      balance: 3500.00,
    },
    {
      name: 'Inventory',
      accountType: 'ASSET',
      description: 'Value of inventory',
      balance: 15000.00,
    },
    {
      name: 'Accounts Payable',
      accountType: 'LIABILITY',
      description: 'Money owed to suppliers',
      balance: 4500.00,
    },
    {
      name: 'Sales Revenue',
      accountType: 'REVENUE',
      description: 'Income from sales',
      balance: 35000.00,
    },
    {
      name: 'Cost of Goods Sold',
      accountType: 'EXPENSE',
      description: 'Cost of products sold',
      balance: 18000.00,
    },
    {
      name: 'Rent Expense',
      accountType: 'EXPENSE',
      description: 'Monthly rent',
      balance: 2000.00,
    },
    {
      name: 'Utilities Expense',
      accountType: 'EXPENSE',
      description: 'Electricity, water, etc.',
      balance: 800.00,
    },
    {
      name: 'Salaries Expense',
      accountType: 'EXPENSE',
      description: 'Employee salaries',
      balance: 12000.00,
    },
  ];

  const createdAccounts = [];
  for (const account of accounts) {
    const createdAccount = await prisma.account.create({
      data: account,
    });
    createdAccounts.push(createdAccount);
  }

  // Create fiscal year
  await prisma.fiscalYear.create({
    data: {
      name: '2023',
      startDate: new Date('2023-01-01'),
      endDate: new Date('2023-12-31'),
      isClosed: false,
    },
  });

  return createdAccounts;
}

async function createTransactions(accounts, orders) {
  const transactionTypes = ['SALE', 'PURCHASE', 'EXPENSE', 'INCOME'];
  const transactions = [];
  
  // Create transactions based on orders
  for (const order of orders) {
    if (order.status === 'COMPLETED') {
      // Create a sale transaction
      const transaction = await prisma.transaction.create({
        data: {
          transactionNo: `TRX-${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`,
          description: `Sale - Order #${order.id}`,
          amount: order.total,
          transactionType: 'SALE',
          transactionDate: order.orderDate,
          reference: `ORD-${order.id}`,
        },
      });
      
      // Create transaction accounts
      await prisma.transactionAccount.create({
        data: {
          transactionId: transaction.id,
          accountId: accounts.find(a => a.name === 'Cash').id, // Debit cash
          amount: order.total,
          isDebit: true,
        },
      });
      
      await prisma.transactionAccount.create({
        data: {
          transactionId: transaction.id,
          accountId: accounts.find(a => a.name === 'Sales Revenue').id, // Credit sales revenue
          amount: order.total,
          isDebit: false,
        },
      });
      
      transactions.push(transaction);
    }
  }
  
  // Create additional random transactions
  for (let i = 0; i < 15; i++) {
    const transactionType = transactionTypes[Math.floor(Math.random() * transactionTypes.length)];
    let amount, description, debitAccount, creditAccount;
    
    switch (transactionType) {
      case 'PURCHASE':
        amount = Math.floor(Math.random() * 1000) + 500;
        description = `Purchase - Invoice #INV-${Math.floor(Math.random() * 1000)}`;
        debitAccount = accounts.find(a => a.name === 'Inventory').id;
        creditAccount = accounts.find(a => a.name === 'Cash').id;
        break;
      case 'EXPENSE':
        amount = Math.floor(Math.random() * 500) + 100;
        description = `Expense - ${['Rent', 'Utilities', 'Salaries', 'Supplies'][Math.floor(Math.random() * 4)]}`;
        debitAccount = accounts.find(a => a.name.includes('Expense')).id;
        creditAccount = accounts.find(a => a.name === 'Cash').id;
        break;
      case 'INCOME':
        amount = Math.floor(Math.random() * 300) + 100;
        description = `Income - ${['Interest', 'Refund', 'Other'][Math.floor(Math.random() * 3)]}`;
        debitAccount = accounts.find(a => a.name === 'Cash').id;
        creditAccount = accounts.find(a => a.name === 'Sales Revenue').id;
        break;
      default:
        amount = Math.floor(Math.random() * 200) + 50;
        description = `Miscellaneous Transaction`;
        debitAccount = accounts.find(a => a.name === 'Cash').id;
        creditAccount = accounts.find(a => a.name === 'Sales Revenue').id;
    }
    
    const transaction = await prisma.transaction.create({
      data: {
        transactionNo: `TRX-${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`,
        description: description,
        amount: amount,
        transactionType: transactionType,
        transactionDate: new Date(Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000), // Random date in the last 30 days
        reference: `REF-${Math.floor(Math.random() * 1000)}`,
      },
    });
    
    // Create transaction accounts
    await prisma.transactionAccount.create({
      data: {
        transactionId: transaction.id,
        accountId: debitAccount,
        amount: amount,
        isDebit: true,
      },
    });
    
    await prisma.transactionAccount.create({
      data: {
        transactionId: transaction.id,
        accountId: creditAccount,
        amount: amount,
        isDebit: false,
      },
    });
    
    transactions.push(transaction);
  }
  
  return transactions;
}

async function createTaxRatesAndCategories() {
  // Create tax rates
  const taxRates = [
    {
      name: 'Standard Rate',
      rate: 7.0, // 7%
      description: 'Standard tax rate',
      isDefault: true,
    },
    {
      name: 'Reduced Rate',
      rate: 3.5, // 3.5%
      description: 'Reduced tax rate for certain goods',
      isDefault: false,
    },
    {
      name: 'Zero Rate',
      rate: 0.0, // 0%
      description: 'Zero tax rate for exempt goods',
      isDefault: false,
    },
  ];

  const createdTaxRates = [];
  for (const taxRate of taxRates) {
    const createdTaxRate = await prisma.taxRate.create({
      data: taxRate,
    });
    createdTaxRates.push(createdTaxRate);
  }

  // Create tax categories
  const taxCategories = [
    {
      name: 'General Goods',
      description: 'Standard taxable goods',
      taxRateId: createdTaxRates.find(tr => tr.name === 'Standard Rate').id,
    },
    {
      name: 'Essential Goods',
      description: 'Essential goods with reduced tax',
      taxRateId: createdTaxRates.find(tr => tr.name === 'Reduced Rate').id,
    },
    {
      name: 'Exempt Goods',
      description: 'Tax exempt goods',
      taxRateId: createdTaxRates.find(tr => tr.name === 'Zero Rate').id,
    },
  ];

  const createdTaxCategories = [];
  for (const taxCategory of taxCategories) {
    const createdTaxCategory = await prisma.taxCategory.create({
      data: taxCategory,
    });
    createdTaxCategories.push(createdTaxCategory);
  }

  return { taxRates: createdTaxRates, taxCategories: createdTaxCategories };
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
