const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL || "mysql://admin@localhost:3306/pos"
    }
  }
});

async function checkConstraints() {
  try {
    console.log('🔍 Checking database constraints...');

    // Check current constraints on subcategory table
    const constraints = await prisma.$queryRaw`
      SELECT
        CONSTRAINT_NAME,
        COLUMN_NAME,
        ORDINAL_POSITION
      FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
      WHERE TABLE_SCHEMA = 'pos'
        AND TABLE_NAME = 'subcategory'
      ORDER BY CONSTRAINT_NAME, ORDINAL_POSITION;
    `;

    console.log('\n📋 Current constraints on subcategory table:');
    console.table(constraints);

    // Check for the specific unique constraint
    const uniqueConstraints = await prisma.$queryRaw`
      SELECT
        CONSTRAINT_NAME,
        GROUP_CONCAT(COLUMN_NAME ORDER BY ORDINAL_POSITION) as COLUMNS
      FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
      WHERE TABLE_SCHEMA = 'pos'
        AND TABLE_NAME = 'subcategory'
        AND CONSTRAINT_NAME LIKE '%name%categoryId%'
      GROUP BY CONSTRAINT_NAME;
    `;

    console.log('\n🔍 Unique constraints with name and categoryId:');
    console.table(uniqueConstraints);

    // Let's also check for any duplicate combinations manually
    const duplicateCheck = await prisma.$queryRaw`
      SELECT name, categoryId, COUNT(*) as count
      FROM subcategory
      GROUP BY name, categoryId
      HAVING COUNT(*) > 1;
    `;

    console.log('\n🔍 Checking for actual duplicates in data:');
    if (duplicateCheck.length === 0) {
      console.log('✅ No duplicates found in database');
    } else {
      console.table(duplicateCheck);
    }

  } catch (error) {
    console.error('❌ Error checking constraints:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkConstraints();
