const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL || "mysql://admin@localhost:3306/pos"
    }
  }
});

async function checkRawDuplicates() {
  try {
    console.log('🔍 Checking for raw duplicates in subcategory table...');
    
    // Check for actual duplicates using raw SQL
    const duplicates = await prisma.$queryRaw`
      SELECT name, categoryId, COUNT(*) as count, GROUP_CONCAT(id) as ids
      FROM subcategory 
      GROUP BY name, categoryId 
      HAVING COUNT(*) > 1
      ORDER BY count DESC;
    `;
    
    console.log('\n📋 Raw duplicate check results:');
    if (duplicates.length === 0) {
      console.log('✅ No duplicates found in raw SQL query');
    } else {
      console.log(`⚠️  Found ${duplicates.length} groups of duplicates:`);
      duplicates.forEach((dup, index) => {
        console.log(`${index + 1}. Name: "${dup.name}", CategoryId: ${dup.categoryId}, Count: ${dup.count}, IDs: ${dup.ids}`);
      });
      
      // Delete duplicates, keeping the first one
      for (const dup of duplicates) {
        const ids = dup.ids.split(',').map(id => parseInt(id));
        const toKeep = ids[0];
        const toDelete = ids.slice(1);
        
        console.log(`\n🔧 Fixing "${dup.name}" in category ${dup.categoryId}:`);
        console.log(`   ✅ Keeping ID: ${toKeep}`);
        console.log(`   🗑️  Deleting IDs: ${toDelete.join(', ')}`);
        
        for (const deleteId of toDelete) {
          await prisma.$executeRaw`DELETE FROM subcategory WHERE id = ${deleteId}`;
          console.log(`   ✅ Deleted ID: ${deleteId}`);
        }
      }
    }
    
    // Check final count
    const finalCount = await prisma.$queryRaw`SELECT COUNT(*) as total FROM subcategory`;
    console.log(`\n📊 Final subcategory count: ${finalCount[0].total}`);
    
  } catch (error) {
    console.error('❌ Error checking duplicates:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkRawDuplicates();
