const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL || "mysql://admin@localhost:3306/pos"
    }
  }
});

async function cleanupDuplicateSubcategories() {
  try {
    console.log('🔍 Checking for duplicate subcategories...');

    // Get all subcategories
    const allSubcategories = await prisma.subCategory.findMany({
      orderBy: [
        { categoryId: 'asc' },
        { name: 'asc' },
        { id: 'asc' }
      ]
    });

    console.log(`📊 Found ${allSubcategories.length} total subcategories`);

    // Check for problematic data
    console.log('\n🔍 Checking for problematic data...');
    const problematic = allSubcategories.filter(sub =>
      !sub.name || sub.name.trim() === '' || !sub.categoryId
    );

    if (problematic.length > 0) {
      console.log(`⚠️  Found ${problematic.length} subcategories with empty names or null categoryId:`);
      problematic.forEach(sub => {
        console.log(`   ID: ${sub.id}, Name: "${sub.name}", CategoryId: ${sub.categoryId}`);
      });
    }

    // Group by name and categoryId to find duplicates (exact match)
    const grouped = {};
    allSubcategories.forEach(sub => {
      const key = `${sub.name}-${sub.categoryId}`;
      if (!grouped[key]) {
        grouped[key] = [];
      }
      grouped[key].push(sub);
    });

    // Find duplicates
    const duplicates = Object.values(grouped).filter(group => group.length > 1);

    if (duplicates.length === 0) {
      console.log('✅ No duplicates found!');

      // But let's check if there are any empty names that need fixing
      if (problematic.length > 0) {
        console.log('\n🔧 Fixing problematic data...');
        for (const sub of problematic) {
          if (!sub.name || sub.name.trim() === '') {
            console.log(`   Deleting subcategory with empty name: ID ${sub.id}`);
            await prisma.subCategory.delete({ where: { id: sub.id } });
          }
        }
      }
      return;
    }

    console.log(`⚠️  Found ${duplicates.length} groups of duplicates:`);

    for (const group of duplicates) {
      console.log(`\n📋 Duplicate group: "${group[0].name}" in category ${group[0].categoryId}`);
      console.log(`   IDs: ${group.map(s => s.id).join(', ')}`);

      // Keep the first one (lowest ID), delete the rest
      const toKeep = group[0];
      const toDelete = group.slice(1);

      console.log(`   ✅ Keeping ID: ${toKeep.id}`);
      console.log(`   🗑️  Deleting IDs: ${toDelete.map(s => s.id).join(', ')}`);

      // Delete duplicates
      for (const duplicate of toDelete) {
        await prisma.subCategory.delete({
          where: { id: duplicate.id }
        });
        console.log(`   ✅ Deleted subcategory ID: ${duplicate.id}`);
      }
    }

    console.log('\n🎉 Cleanup completed successfully!');

  } catch (error) {
    console.error('❌ Error during cleanup:', error);
  } finally {
    await prisma.$disconnect();
  }
}

cleanupDuplicateSubcategories();
